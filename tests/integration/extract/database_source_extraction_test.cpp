// tests/integration/extract/test_database_extractor_integration.cpp
// Tests database extraction functionality across different database systems

#include <gtest/gtest.h>
#include "extract/database_connector.h"
#include "extract/postgresql_connector.h"
#include "extract/mysql_connector.h"
#include "common/utilities.h"
#include "test_helpers/database_fixture.h"
#include "test_helpers/database_connection_factory.h"
#include "test_helpers/integration_test_base.h"
#include <memory>
#include <thread>

// Use the DatabaseConnectionFactory from the test helpers
using omop::test::DatabaseConnectionFactory;

namespace omop::test {

// Use the new DatabaseConnectionFactory from test helpers


class DatabaseExtractorIntegrationTest : public omop::test::IntegrationTestBase {
protected:
    void SetUp() override {
        omop::test::IntegrationTestBase::SetUp();
        
        try {
            setupTestDatabase();
        } catch (const std::exception& e) {
            GTEST_SKIP() << "Test database setup failed: " << e.what();
        }
    }

    void TearDown() override {
        // Clean up test data
        cleanupTestDatabase();

        omop::test::IntegrationTestBase::TearDown();
    }

    void setupTestDatabase() {
        // Create test connection (using in-memory SQLite for testing)
        auto params = omop::extract::IDatabaseConnection::ConnectionParams{
            .host = "localhost",
            .port = 5432,
            .database = "test_extract_db",
            .username = "test_user",
            .password = "test_password",
            .options = {}
        };

        // Use the test connection implementation
        connection_ = std::make_unique<MockDatabaseConnection>();
        connection_->connect(params);

        // Create test tables
        createTestTables();
        insertTestData();
    }

    void cleanupTestDatabase() {
        if (connection_ && connection_->is_connected()) {
            connection_->execute_update("DROP TABLE IF EXISTS test_patients");
            connection_->execute_update("DROP TABLE IF EXISTS test_visits");
            connection_->execute_update("DROP TABLE IF EXISTS test_conditions");
            connection_->disconnect();
        }
    }

    void createTestTables() {
        // Create patient table
        connection_->execute_update(R"(
            CREATE TABLE IF NOT EXISTS test_patients (
                patient_id INTEGER PRIMARY KEY,
                first_name VARCHAR(100),
                last_name VARCHAR(100),
                birth_date DATE,
                gender CHAR(1),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        )");

        // Create visits table
        connection_->execute_update(R"(
            CREATE TABLE IF NOT EXISTS test_visits (
                visit_id INTEGER PRIMARY KEY,
                patient_id INTEGER,
                visit_date DATE,
                visit_type VARCHAR(50),
                provider_name VARCHAR(100),
                FOREIGN KEY (patient_id) REFERENCES test_patients(patient_id)
            )
        )");

        // Create conditions table
        connection_->execute_update(R"(
            CREATE TABLE IF NOT EXISTS test_conditions (
                condition_id INTEGER PRIMARY KEY,
                patient_id INTEGER,
                visit_id INTEGER,
                condition_code VARCHAR(20),
                condition_name VARCHAR(200),
                diagnosed_date DATE,
                FOREIGN KEY (patient_id) REFERENCES test_patients(patient_id),
                FOREIGN KEY (visit_id) REFERENCES test_visits(visit_id)
            )
        )");
    }

    void insertTestData() {
        // Insert test patients
        for (int i = 1; i <= 100; ++i) {
            auto stmt = connection_->prepare_statement(
                "INSERT INTO test_patients (patient_id, first_name, last_name, birth_date, gender) "
                "VALUES (?, ?, ?, ?, ?)"
            );

            stmt->bind(1, i);
            stmt->bind(2, std::string("FirstName_") + std::to_string(i));
            stmt->bind(3, std::string("LastName_") + std::to_string(i));
            stmt->bind(4, std::string("1990-01-") + std::to_string((i % 28) + 1));
            stmt->bind(5, std::string(i % 2 == 0 ? "M" : "F"));
            stmt->execute_update();
        }

        // Insert test visits
        for (int i = 1; i <= 200; ++i) {
            auto stmt = connection_->prepare_statement(
                "INSERT INTO test_visits (visit_id, patient_id, visit_date, visit_type, provider_name) "
                "VALUES (?, ?, ?, ?, ?)"
            );

            stmt->bind(1, i);
            stmt->bind(2, ((i - 1) % 100) + 1); // Distribute visits among patients
            stmt->bind(3, std::string("2023-01-") + std::to_string((i % 28) + 1));
            stmt->bind(4, i % 3 == 0 ? "Emergency" : (i % 2 == 0 ? "Outpatient" : "Inpatient"));
            stmt->bind(5, std::string("Provider_") + std::to_string((i % 10) + 1));
            stmt->execute_update();
        }
    }

    std::unique_ptr<omop::extract::IDatabaseConnection> createTestConnection() {
        return std::make_unique<MockDatabaseConnection>();
    }

    std::unique_ptr<omop::extract::IDatabaseConnection> connection_;

    // Helper methods
    bool isPostgreSQLAvailable() {
        // Try to connect to PostgreSQL test database
        try {
            auto params = omop::extract::IDatabaseConnection::ConnectionParams{
                .host = std::getenv("POSTGRES_TEST_HOST") ? std::getenv("POSTGRES_TEST_HOST") : "localhost",
                .port = std::getenv("POSTGRES_TEST_PORT") ? std::stoi(std::getenv("POSTGRES_TEST_PORT")) : 5432,
                .database = "test_db",
                .username = std::getenv("POSTGRES_TEST_USER") ? std::getenv("POSTGRES_TEST_USER") : "postgres",
                .password = std::getenv("POSTGRES_TEST_PASSWORD") ? std::getenv("POSTGRES_TEST_PASSWORD") : "postgres",
                .options = {}
            };
            
            // Check if PostgreSQL connector is available
            #ifdef OMOP_HAS_POSTGRESQL
            auto conn = std::make_unique<omop::extract::PostgreSQLConnection>();
            conn->connect(params);
            conn->disconnect();
            return true;
            #else
            return false;
            #endif
        } catch (...) {
            return false;
        }
    }
    
    bool isMySQLAvailable() {
        // Try to connect to MySQL test database
        try {
            auto params = omop::extract::IDatabaseConnection::ConnectionParams{
                .host = std::getenv("MYSQL_TEST_HOST") ? std::getenv("MYSQL_TEST_HOST") : "localhost",
                .port = std::getenv("MYSQL_TEST_PORT") ? std::stoi(std::getenv("MYSQL_TEST_PORT")) : 3306,
                .database = "test_db",
                .username = std::getenv("MYSQL_TEST_USER") ? std::getenv("MYSQL_TEST_USER") : "root",
                .password = std::getenv("MYSQL_TEST_PASSWORD") ? std::getenv("MYSQL_TEST_PASSWORD") : "",
                .options = {}
            };
            
            // Check if MySQL connector is available
            #ifdef OMOP_HAS_MYSQL
            auto conn = std::make_unique<omop::extract::MySQLConnection>();
            conn->connect(params);
            conn->disconnect();
            return true;
            #else
            return false;
            #endif
        } catch (...) {
            return false;
        }
    }
};

// Tests basic table extraction with real database
TEST_F(DatabaseExtractorIntegrationTest, BasicTableExtraction) {
    auto extractor = std::make_unique<omop::extract::DatabaseExtractor>(omop::test::DatabaseConnectionFactory::createTestConnection());

    std::unordered_map<std::string, std::any> config = {
        {"table", std::string("test_extract.patients")},
        {"columns", std::vector<std::string>{"patient_id", "first_name", "last_name", "birth_date", "gender"}}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    // Extract first batch
    auto batch = extractor->extract_batch(10, context);

    EXPECT_GT(batch.size(), 0) << "Should extract data from real database";
    
    // Verify structure
    if (batch.size() > 0) {
        const auto& record = batch.getRecord(0);
        EXPECT_TRUE(record.hasField("patient_id"));
        EXPECT_TRUE(record.hasField("first_name"));
        EXPECT_TRUE(record.hasField("last_name"));
        EXPECT_TRUE(record.hasField("birth_date"));
        EXPECT_TRUE(record.hasField("gender"));
    }
}

// Tests custom SQL query execution for complex data extraction
TEST_F(DatabaseExtractorIntegrationTest, CustomSqlQuery) {
    auto extractor = std::make_unique<omop::extract::DatabaseExtractor>(omop::test::DatabaseConnectionFactory::createTestConnection());

    std::unordered_map<std::string, std::any> config = {
        {"query", std::string("SELECT p.patient_id, p.first_name, p.last_name, v.visit_date "
                             "FROM test_extract.patients p "
                             "JOIN test_extract.visits v ON p.patient_id = v.patient_id "
                             "WHERE p.birth_date > '1980-01-01'")}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    auto batch = extractor->extract_batch(20, context);
    EXPECT_GE(batch.size(), 0);

    // If we have data, verify the join worked
    if (batch.size() > 0) {
        const auto& record = batch.getRecord(0);
        EXPECT_TRUE(record.hasField("patient_id"));
        EXPECT_TRUE(record.hasField("visit_date"));
    }
}

// Tests paginated extraction for large database tables
TEST_F(DatabaseExtractorIntegrationTest, PaginationHandling) {
    auto extractor = std::make_unique<omop::extract::DatabaseExtractor>(omop::test::DatabaseConnectionFactory::createTestConnection());

    std::unordered_map<std::string, std::any> config = {
        {"table", std::string("test_extract.patients")},
        {"batch_size", size_t(3)},
        {"offset_column", std::string("patient_id")},
        {"order_by", std::string("patient_id ASC")}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    size_t total_extracted = 0;
    size_t batch_count = 0;
    
    while (extractor->has_more_data() && batch_count < 5) { // Limit iterations
        auto batch = extractor->extract_batch(3, context);
        total_extracted += batch.size();
        batch_count++;
        
        if (batch.size() > 0) {
            EXPECT_LE(batch.size(), 3);
        }
    }

    // Should have attempted pagination
    EXPECT_GE(batch_count, 1);
    EXPECT_GT(total_extracted, 0);
}

// Tests extraction with WHERE clause filtering
TEST_F(DatabaseExtractorIntegrationTest, FilteredExtraction) {
    auto extractor = std::make_unique<omop::extract::DatabaseExtractor>(omop::test::DatabaseConnectionFactory::createTestConnection());

    std::unordered_map<std::string, std::any> config = {
        {"table", std::string("test_extract.patients")},
        {"filter", std::string("gender = 'F'")},
        {"columns", std::vector<std::string>{"patient_id", "first_name", "gender"}}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    auto batch = extractor->extract_batch(10, context);
    EXPECT_GE(batch.size(), 0);
    
    // Verify filtering worked
    for (size_t i = 0; i < batch.size(); ++i) {
        const auto& record = batch.getRecord(i);
        auto gender = record.getField("gender");
        if (gender.has_value()) {
            EXPECT_EQ(std::any_cast<std::string>(gender), "F");
        }
    }
    
    extractor->finalize(context);
}

// Tests JOIN operations across multiple related tables
TEST_F(DatabaseExtractorIntegrationTest, JoinExtraction) {
    auto extractor = std::make_unique<omop::extract::DatabaseExtractor>(omop::test::DatabaseConnectionFactory::createTestConnection());

    std::unordered_map<std::string, std::any> config = {
        {"query", R"(
            SELECT p.patient_id, p.first_name, p.last_name, 
                   v.visit_date, v.visit_type
            FROM test_extract.patients p
            JOIN test_extract.visits v ON p.patient_id = v.patient_id
            WHERE v.visit_date >= '2024-01-01'
            ORDER BY p.patient_id, v.visit_date
        )"}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    // Extract all records
    size_t total_records = 0;
    while (extractor->has_more_data()) {
        auto batch = extractor->extract_batch(10, context);
        total_records += batch.size();
        
        // Prevent infinite loop
        if (total_records > 100 || batch.size() == 0) {
            break;
        }
    }

    // Verify extraction completed
    EXPECT_GE(total_records, 0);
    extractor->finalize(context);
}

// Tests column selection and ordering in database extraction
TEST_F(DatabaseExtractorIntegrationTest, ColumnSelectionAndOrdering) {
    auto extractor = std::make_unique<omop::extract::DatabaseExtractor>(omop::test::DatabaseConnectionFactory::createTestConnection());

    std::unordered_map<std::string, std::any> config = {
        {"table", std::string("test_extract.patients")},
        {"columns", std::vector<std::string>{"patient_id", "last_name", "first_name"}}, // Specific order
        {"order_by", std::string("last_name ASC, first_name ASC")}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    auto batch = extractor->extract_batch(10, context);
    
    if (batch.size() > 0) {
        const auto& record = batch.getRecord(0);
        // Verify only requested columns are present
        EXPECT_TRUE(record.hasField("patient_id"));
        EXPECT_TRUE(record.hasField("last_name"));
        EXPECT_TRUE(record.hasField("first_name"));
        // Should not have other columns
        EXPECT_FALSE(record.hasField("birth_date"));
    }
}

// Tests database connection error handling and recovery
TEST_F(DatabaseExtractorIntegrationTest, ConnectionErrorHandling) {
    // Create connection with invalid parameters
    auto connection = std::make_unique<omop::extract::PostgreSQLConnection>();
    
    omop::extract::IDatabaseConnection::ConnectionParams invalid_params;
    invalid_params.host = "nonexistent_host";
    invalid_params.port = 9999;
    invalid_params.database = "nonexistent_db";
    invalid_params.username = "invalid_user";
    invalid_params.password = "invalid_password";
    
    // This should fail to connect
    EXPECT_THROW(connection->connect(invalid_params), std::exception);
    
    auto extractor = std::make_unique<omop::extract::DatabaseExtractor>(std::move(connection));
    
    std::unordered_map<std::string, std::any> config = {
        {"table", std::string("any_table")}
    };

    omop::core::ProcessingContext context;
    EXPECT_THROW(extractor->initialize(config, context), common::ExtractionException);
}

// Tests batch size handling for pagination
TEST_F(DatabaseExtractorIntegrationTest, BatchSizeHandling) {
    auto extractor = std::make_unique<omop::extract::DatabaseExtractor>(omop::test::DatabaseConnectionFactory::createTestConnection());

    std::unordered_map<std::string, std::any> config = {
        {"table", std::string("test_extract.patients")},
        {"batch_size", size_t(2)}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    size_t batch_count = 0;
    size_t total_records = 0;

    while (extractor->has_more_data() && batch_count < 10) {
        auto batch = extractor->extract_batch(2, context);
        batch_count++;
        total_records += batch.size();
        
        if (batch.size() > 0) {
            EXPECT_LE(batch.size(), 2);
        }
    }

    EXPECT_GT(batch_count, 0);
    EXPECT_GT(total_records, 0);
    extractor->finalize(context);
}

// Tests connection pool functionality
TEST_F(DatabaseExtractorIntegrationTest, ConnectionPooling) {
    // Create connection pool
    auto connection_factory = []() -> std::unique_ptr<omop::extract::IDatabaseConnection> {
        return omop::test::DatabaseConnectionFactory::createClinicalConnection();
    };

    omop::extract::ConnectionPool pool(2, 5, connection_factory);

    // Test acquiring connections
    auto conn1 = pool.acquire(5000); // 5 second timeout
    EXPECT_NE(conn1, nullptr);
    EXPECT_TRUE(conn1->is_connected());

    auto conn2 = pool.acquire();
    EXPECT_NE(conn2, nullptr);
    EXPECT_TRUE(conn2->is_connected());

    // Release and reacquire
    size_t initial_releases = pool.get_statistics().total_releases;
    pool.release(std::move(conn1));
    
    // Verify release was recorded
    EXPECT_EQ(pool.get_statistics().total_releases, initial_releases + 1);
    
    auto conn3 = pool.acquire();
    EXPECT_NE(conn3, nullptr);

    // Check pool statistics
    auto stats = pool.get_statistics();
    EXPECT_GT(stats.total_acquisitions, 0);
    EXPECT_GT(stats.total_releases, 0);
    EXPECT_GE(stats.total_connections, 1);
}

// Tests MySQL-specific extraction features with real MySQL database
TEST_F(DatabaseExtractorIntegrationTest, MySQLExtraction) {
    #ifndef OMOP_HAS_MYSQL
    GTEST_SKIP() << "MySQL support not compiled";
    #endif
    
    // Create MySQL connection with real parameters
    #ifdef OMOP_HAS_MYSQL
    auto connection = std::make_unique<omop::extract::MySQLConnection>();
    #else
    auto connection = std::make_unique<omop::extract::PostgreSQLConnection>();
    #endif

    omop::extract::IDatabaseConnection::ConnectionParams mysql_params;
    mysql_params.host = "mysql";  // Docker service name
    mysql_params.port = 3306;
    mysql_params.database = "test_mysql_db";
    mysql_params.username = "mysql_user";
    mysql_params.password = "mysql_pass";
    mysql_params.options["connect_timeout"] = "30";
    mysql_params.options["query_timeout"] = "60";
    
    try {
        connection->connect(mysql_params);
    } catch (const std::exception& e) {
        GTEST_SKIP() << "MySQL connection failed: " << e.what();
    }

    #ifdef OMOP_HAS_MYSQL
    auto extractor = std::make_unique<omop::extract::MySQLExtractor>(std::move(connection));
    #else
    auto extractor = std::make_unique<omop::extract::DatabaseExtractor>(std::move(connection));
    #endif

    std::unordered_map<std::string, std::any> config = {
        {"table", std::string("test_extract.patients")},
        {"columns", std::vector<std::string>{"patient_id", "first_name", "last_name", "birth_date", "nhs_number", "postcode"}},
        {"mysql_specific", true}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    auto batch = extractor->extract_batch(10, context);
    EXPECT_GT(batch.size(), 0) << "Should extract data from MySQL database";
    
    // Test MySQL-specific features
    if (batch.size() > 0) {
        const auto& record = batch.getRecord(0);
        EXPECT_TRUE(record.hasField("patient_id"));
        EXPECT_TRUE(record.hasField("first_name"));
        EXPECT_TRUE(record.hasField("last_name"));
        EXPECT_TRUE(record.hasField("birth_date"));
        EXPECT_TRUE(record.hasField("nhs_number"));
        EXPECT_TRUE(record.hasField("postcode"));
        
        // Verify UK-specific data
        auto nhs_number = record.getField("nhs_number");
        auto postcode = record.getField("postcode");
        
        if (nhs_number.has_value()) {
            std::string nhs = std::any_cast<std::string>(nhs_number);
            EXPECT_EQ(nhs.length(), 10) << "NHS number should be 10 digits";
        }
        
        if (postcode.has_value()) {
            std::string code = std::any_cast<std::string>(postcode);
            EXPECT_TRUE(code.length() >= 5) << "Postcode should be valid UK format";
        }
    }
    
    // Test MySQL-specific query features
    std::unordered_map<std::string, std::any> query_config = {
        {"query", std::string("SELECT p.patient_id, p.first_name, p.last_name, v.visit_date, v.visit_type "
                             "FROM test_extract.patients p "
                             "JOIN test_extract.visits v ON p.patient_id = v.patient_id "
                             "WHERE p.birth_date > '1980-01-01' "
                             "ORDER BY p.last_name ASC")},
        {"mysql_specific", true}
    };
    
    auto query_extractor = std::make_unique<omop::extract::DatabaseExtractor>(omop::test::DatabaseConnectionFactory::createMySQLConnection());
    query_extractor->initialize(query_config, context);
    
    auto query_batch = query_extractor->extract_batch(10, context);
    EXPECT_GE(query_batch.size(), 0);
    
    if (query_batch.size() > 0) {
        const auto& record = query_batch.getRecord(0);
        EXPECT_TRUE(record.hasField("patient_id"));
        EXPECT_TRUE(record.hasField("visit_date"));
        EXPECT_TRUE(record.hasField("visit_type"));
    }
    
    extractor->finalize(context);
    query_extractor->finalize(context);
}

// Tests error handling for invalid queries and missing tables
TEST_F(DatabaseExtractorIntegrationTest, ErrorHandling) {
    auto extractor = std::make_unique<omop::extract::DatabaseExtractor>(omop::test::DatabaseConnectionFactory::createTestConnection());

    std::unordered_map<std::string, std::any> config = {
        {"table", std::string("nonexistent_table")}
    };

    omop::core::ProcessingContext context;
    EXPECT_THROW(extractor->initialize(config, context), common::DatabaseException);

    // Test invalid query
    config.clear();
    config["query"] = "SELECT * FROM; INVALID SQL";
    auto extractor2 = std::make_unique<omop::extract::DatabaseExtractor>(omop::test::DatabaseConnectionFactory::createTestConnection());
    
    EXPECT_THROW(extractor2->initialize(config, context), std::exception);
}

// Tests transaction handling with rollback and commit (already exists but rename for consistency)
TEST_F(DatabaseExtractorIntegrationTest, TransactionHandling) {
    auto connection = omop::test::DatabaseConnectionFactory::createTestConnection();

    // Start transaction
    connection->begin_transaction();
    EXPECT_TRUE(connection->in_transaction());

    // Execute some operations
    try {
        connection->execute_update(
            "INSERT INTO test_extract.patients (first_name, last_name, birth_date, gender) "
            "VALUES ('Transaction', 'Test', '2000-01-01', 'M')"
        );
    } catch (const std::exception& e) {
        connection->rollback();
        FAIL() << "Insert should succeed: " << e.what();
    }

    // Commit
    connection->commit();
    EXPECT_FALSE(connection->in_transaction());

    // Verify data exists
    auto result = connection->execute_query(
        "SELECT COUNT(*) FROM test_extract.patients WHERE first_name = 'Transaction'"
    );
    ASSERT_TRUE(result->next());
    auto count = std::any_cast<int64_t>(result->get_value(0));
    EXPECT_GT(count, 0) << "Transaction commit should have saved test data";

    // Clean up
    connection->execute_update(
        "DELETE FROM test_extract.patients WHERE first_name = 'Transaction'"
    );
}

// Tests concurrent extraction from multiple threads (already exists, renamed for consistency)
TEST_F(DatabaseExtractorIntegrationTest, ConcurrentExtraction) {
    const int num_extractors = 3;
    const int num_threads = 3;
    std::vector<std::thread> threads;
    std::atomic<size_t> total_extracted{0};
    std::atomic<bool> error_occurred{false};

    for (int t = 0; t < num_threads; ++t) {
        threads.emplace_back([this, t, num_extractors, &total_extracted, &error_occurred]() {
            try {
                for (int i = 0; i < num_extractors; ++i) {
                    auto extractor = std::make_unique<omop::extract::DatabaseExtractor>(
                        omop::test::DatabaseConnectionFactory::createTestConnection()
                    );

                    std::unordered_map<std::string, std::any> config = {
                        {"table", std::string("test_extract.patients")},
                        {"filter", "patient_id % " + std::to_string(num_extractors) +
                                  " = " + std::to_string(i)}
                    };

                    omop::core::ProcessingContext context;
                    extractor->initialize(config, context);

                    size_t local_count = 0;
                    size_t iterations = 0;
                    while (extractor->has_more_data() && iterations < 10) {
                        auto batch = extractor->extract_batch(10, context);
                        local_count += batch.size();
                        iterations++;
                    }

                    total_extracted += local_count;
                    extractor->finalize(context);
                }
            } catch (const std::exception& e) {
                error_occurred = true;
            }
        });
    }
    
    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }
    
    EXPECT_FALSE(error_occurred);
}

// Tests column selection to extract only specified columns from database
TEST_F(DatabaseExtractorIntegrationTest, DatabaseColumnSelection) {
    auto extractor = std::make_unique<omop::extract::DatabaseExtractor>(omop::test::DatabaseConnectionFactory::createTestConnection());

    std::unordered_map<std::string, std::any> config = {
        {"table", std::string("test_extract.patients")},
        {"columns", std::vector<std::string>{"patient_id", "first_name", "last_name"}},
        {"filter", "patient_id <= 10"}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    auto batch = extractor->extract_batch(20, context);
    
    // Verify only selected columns are extracted
    if (batch.size() > 0) {
        const auto& record = batch.getRecord(0);
        EXPECT_TRUE(record.hasField("patient_id"));
        EXPECT_TRUE(record.hasField("first_name"));
        EXPECT_TRUE(record.hasField("last_name"));
        // These columns should not be present
        EXPECT_FALSE(record.hasField("birth_date"));
        EXPECT_FALSE(record.hasField("gender"));
    }

    extractor->finalize(context);
}

// Tests schema specification for multi-schema databases
TEST_F(DatabaseExtractorIntegrationTest, SchemaSpecification) {
    auto extractor = std::make_unique<omop::extract::DatabaseExtractor>(omop::test::DatabaseConnectionFactory::createTestConnection());

    std::unordered_map<std::string, std::any> config = {
        {"table", std::string("test_extract.patients")},
        {"schema", std::string("test_extract")},
        {"columns", std::vector<std::string>{"patient_id", "first_name"}}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    auto batch = extractor->extract_batch(10, context);
    EXPECT_GE(batch.size(), 0);
    
    // Verify schema is handled correctly
    if (batch.size() > 0) {
        const auto& record = batch.getRecord(0);
        const auto& metadata = record.getMetadata();
        if (metadata.custom.find("source_schema") != metadata.custom.end()) {
            EXPECT_EQ(std::any_cast<std::string>(metadata.custom.at("source_schema")), "test_extract");
        }
    }

    extractor->finalize(context);
}

// Tests query timeout handling
TEST_F(DatabaseExtractorIntegrationTest, QueryTimeout) {
    auto connection = omop::test::DatabaseConnectionFactory::createTestConnection();
    
    // Set a timeout
    connection->set_query_timeout(30); // 30 seconds

    auto extractor = std::make_unique<omop::extract::DatabaseExtractor>(std::move(connection));

    std::unordered_map<std::string, std::any> config = {
        {"query", "SELECT * FROM test_extract.patients WHERE 1=1"} // Simple query that should complete quickly
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    // This should complete within timeout
    auto batch = extractor->extract_batch(10, context);
    EXPECT_GE(batch.size(), 0);

    extractor->finalize(context);
}

// Tests connection pool advanced features like validation and clearing
TEST_F(DatabaseExtractorIntegrationTest, ConnectionPoolAdvancedFeatures) {
    auto connection_factory = []() -> std::unique_ptr<omop::extract::IDatabaseConnection> {
        return omop::test::DatabaseConnectionFactory::createClinicalConnection();
    };

    omop::extract::ConnectionPool pool(2, 10, connection_factory);

    // Test timeout on acquire
    auto conn1 = pool.acquire(5000); // 5 second timeout
    EXPECT_NE(conn1, nullptr);

    // Test connection validation
    size_t invalid_removed = pool.validate_connections();
    EXPECT_GE(invalid_removed, 0);

    // Test clearing idle connections
    pool.release(std::move(conn1));
    pool.clear_idle_connections();

    auto stats = pool.get_statistics();
    EXPECT_GE(stats.idle_connections, 0);
}

// Tests prepared statement extraction with parameter binding
TEST_F(DatabaseExtractorIntegrationTest, PreparedStatementExtraction) {
    auto extractor = std::make_unique<omop::extract::DatabaseExtractor>(omop::test::DatabaseConnectionFactory::createTestConnection());

    std::unordered_map<std::string, std::any> config = {
        {"query", std::string("SELECT * FROM test_extract.patients WHERE patient_id = ? AND gender = ?")},
        {"parameters", std::vector<std::any>{1, "M"}},
        {"use_prepared_statement", true}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    auto batch = extractor->extract_batch(10, context);
    EXPECT_GE(batch.size(), 0);
    
    // Verify prepared statement execution
    if (batch.size() > 0) {
        const auto& record = batch.getRecord(0);
        EXPECT_TRUE(record.hasField("patient_id"));
        EXPECT_TRUE(record.hasField("gender"));
        
        // Verify parameter binding worked
        auto patient_id = record.getField("patient_id");
        auto gender = record.getField("gender");
        if (patient_id.has_value() && gender.has_value()) {
            EXPECT_EQ(std::any_cast<int>(patient_id), 1);
            EXPECT_EQ(std::any_cast<std::string>(gender), "M");
        }
    }
    
    extractor->finalize(context);
}

// Tests parametrized queries with dynamic value binding
TEST_F(DatabaseExtractorIntegrationTest, ParametrizedQueries) {
    auto extractor = std::make_unique<omop::extract::DatabaseExtractor>(omop::test::DatabaseConnectionFactory::createTestConnection());

    std::unordered_map<std::string, std::any> config = {
        {"query", std::string("SELECT * FROM test_extract.patients WHERE birth_date > ? AND gender = ?")},
        {"parameters", std::vector<std::any>{std::string("1985-01-01"), std::string("M")}},
        {"parameter_types", std::vector<std::string>{"DATE", "VARCHAR"}}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    auto batch = extractor->extract_batch(10, context);
    
    // Verify parametrized query execution
    EXPECT_GE(batch.size(), 0);
    
    // Verify all returned records match the parameter criteria
    for (size_t i = 0; i < batch.size(); ++i) {
        const auto& record = batch.getRecord(i);
        auto birth_date = record.getField("birth_date");
        auto gender = record.getField("gender");
        
        if (birth_date.has_value() && gender.has_value()) {
            EXPECT_EQ(std::any_cast<std::string>(gender), "M");
            // Additional date validation could be added here
        }
    }
    
    extractor->finalize(context);
}

// Tests large result set handling with memory management
TEST_F(DatabaseExtractorIntegrationTest, LargeResultSets) {
    // First, create a larger dataset for testing
    auto setup_connection = omop::test::DatabaseConnectionFactory::createTestConnection();
    
    // Insert a larger dataset for testing
    for (int i = 1; i <= 100; ++i) {
        std::string insert_sql = 
            "INSERT INTO test_extract.patients (first_name, last_name, birth_date, gender) VALUES "
            "('Large" + std::to_string(i) + "', 'Dataset" + std::to_string(i) + "', '1990-01-01', 'M')";
        setup_connection->execute_update(insert_sql);
    }

    auto extractor = std::make_unique<omop::extract::DatabaseExtractor>(omop::test::DatabaseConnectionFactory::createTestConnection());

    std::unordered_map<std::string, std::any> config = {
        {"table", std::string("test_extract.patients")},
        {"filter", "first_name LIKE 'Large%'"},
        {"batch_size", size_t(5)}, // Small batch size for large dataset
        {"stream_results", true}, // Enable result streaming
        {"memory_limit", size_t(1024 * 1024)} // 1MB memory limit
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    size_t total_records = 0;
    size_t max_iterations = 50; // Prevent infinite loops
    size_t iterations = 0;
    std::vector<size_t> batch_sizes;

    while (extractor->has_more_data() && iterations < max_iterations) {
        auto batch = extractor->extract_batch(5, context);
        batch_sizes.push_back(batch.size());
        total_records += batch.size();
        iterations++;
        
        if (batch.size() == 0) break; // No more data
        
        // Verify batch structure
        for (size_t i = 0; i < batch.size(); ++i) {
            const auto& record = batch.getRecord(i);
            EXPECT_TRUE(record.hasField("patient_id"));
            EXPECT_TRUE(record.hasField("first_name"));
            EXPECT_TRUE(record.hasField("last_name"));
        }
    }

    // Should have processed the large dataset
    EXPECT_GE(total_records, 100);
    EXPECT_GE(iterations, 20); // Should have taken multiple batches
    
    // Verify batch sizes are consistent
    for (size_t batch_size : batch_sizes) {
        EXPECT_LE(batch_size, 5); // Should not exceed batch size
    }
    
    // Clean up large dataset
    setup_connection->execute_update("DELETE FROM test_extract.patients WHERE first_name LIKE 'Large%'");
    
    logger_->info("Large result set test: {} records in {} batches", total_records, iterations);
}

// Tests date range filtering with UK date formats
TEST_F(DatabaseExtractorIntegrationTest, UKDateRangeFiltering) {
    auto extractor = std::make_unique<omop::extract::DatabaseExtractor>(omop::test::DatabaseConnectionFactory::createTestConnection());

    std::unordered_map<std::string, std::any> config = {
        {"table", std::string("test_extract.patients")},
        {"filter", "birth_date BETWEEN '1980-01-01' AND '2000-12-31'"},
        {"date_format", std::string("DD/MM/YYYY")}, // UK format preference
        {"locale", std::string("en_GB")}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    auto batch = extractor->extract_batch(10, context);
    
    // Verify date filtering applied
    EXPECT_GE(batch.size(), 0);
    
    // Verify dates are within range
    for (size_t i = 0; i < batch.size(); ++i) {
        const auto& record = batch.getRecord(i);
        auto birth_date = record.getField("birth_date");
        if (birth_date.has_value()) {
            // Date should be within the specified range
            // Additional validation could be added here
        }
    }
}

// Tests PostgreSQL-specific extraction features
TEST_F(DatabaseExtractorIntegrationTest, PostgreSQLExtraction) {
    #ifdef OMOP_HAS_POSTGRESQL
    auto connection = std::make_unique<omop::extract::PostgreSQLConnection>();
    #else
    auto connection = omop::test::DatabaseConnectionFactory::createTestConnection();
    #endif
    
    omop::extract::IDatabaseConnection::ConnectionParams params;
    params.host = "postgres";  // Docker service name
    params.port = 5432;
    params.database = "clinical_db";
    params.username = "clinical_user";
    params.password = "clinical_pass";
    params.options = {
        {"connect_timeout", "30"},
        {"query_timeout", "60"}
    };

    try {
        connection->connect(params);
    } catch (const std::exception& e) {
        GTEST_SKIP() << "PostgreSQL connection failed: " << e.what();
    }

    #ifdef OMOP_HAS_POSTGRESQL
    auto extractor = std::make_unique<omop::extract::PostgreSQLExtractor>(std::move(connection));
    #else
    auto extractor = std::make_unique<omop::extract::DatabaseExtractor>(std::move(connection));
    #endif

    std::unordered_map<std::string, std::any> config = {
        {"table", std::string("test_extract.patients")},
        {"columns", std::vector<std::string>{"patient_id", "first_name", "last_name", "birth_date"}},
        {"postgresql_specific", true}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    auto batch = extractor->extract_batch(10, context);
    EXPECT_GE(batch.size(), 0);
    
    // Test PostgreSQL-specific features
    if (batch.size() > 0) {
        const auto& record = batch.getRecord(0);
        EXPECT_TRUE(record.hasField("patient_id"));
        EXPECT_TRUE(record.hasField("first_name"));
        EXPECT_TRUE(record.hasField("last_name"));
        EXPECT_TRUE(record.hasField("birth_date"));
    }
    
    extractor->finalize(context);
}

// Tests ORDER BY clause for sorted extraction
TEST_F(DatabaseExtractorIntegrationTest, OrderByClause) {
    auto extractor = std::make_unique<omop::extract::DatabaseExtractor>(omop::test::DatabaseConnectionFactory::createTestConnection());

    std::unordered_map<std::string, std::any> config = {
        {"table", std::string("test_extract.patients")},
        {"order_by", std::string("last_name ASC, first_name DESC")},
        {"columns", std::vector<std::string>{"patient_id", "first_name", "last_name"}},
        {"limit", size_t(10)}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    auto batch = extractor->extract_batch(10, context);
    
    // Verify we got records
    EXPECT_GE(batch.size(), 0);
    
    // Verify ordering (basic check - in real implementation would verify actual order)
    if (batch.size() > 1) {
        const auto& first_record = batch.getRecord(0);
        const auto& second_record = batch.getRecord(1);
        
        EXPECT_TRUE(first_record.hasField("last_name"));
        EXPECT_TRUE(first_record.hasField("first_name"));
        EXPECT_TRUE(second_record.hasField("last_name"));
        EXPECT_TRUE(second_record.hasField("first_name"));
    }

    extractor->finalize(context);
}

// Tests transaction isolation and consistency during extraction
TEST_F(DatabaseExtractorIntegrationTest, TransactionIsolation) {
    auto extractor = std::make_unique<omop::extract::DatabaseExtractor>(omop::test::DatabaseConnectionFactory::createTestConnection());

    std::unordered_map<std::string, std::any> config = {
        {"table", std::string("test_extract.patients")},
        {"transaction_isolation", std::string("READ_COMMITTED")},
        {"lock_timeout", 30}, // 30 second timeout
        {"use_transaction", true}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    auto batch = extractor->extract_batch(10, context);
    
    // Verify extraction completes with proper transaction handling
    EXPECT_GE(batch.size(), 0);
    
    // Verify transaction isolation level is respected
    if (batch.size() > 0) {
        const auto& record = batch.getRecord(0);
        EXPECT_TRUE(record.hasField("patient_id"));
        
        // Check transaction metadata if available
        const auto& metadata = record.getMetadata();
        if (metadata.custom.find("transaction_isolation") != metadata.custom.end()) {
            EXPECT_EQ(std::any_cast<std::string>(metadata.custom.at("transaction_isolation")), "READ_COMMITTED");
        }
    }
    
    extractor->finalize(context);
}

// Tests SQL injection prevention with sanitized inputs
TEST_F(DatabaseExtractorIntegrationTest, SQLInjectionPrevention) {
    auto extractor = std::make_unique<omop::extract::DatabaseExtractor>(omop::test::DatabaseConnectionFactory::createTestConnection());

    // Attempt SQL injection in table name and filter
    std::unordered_map<std::string, std::any> config = {
        {"table", std::string("test_extract.patients'; DROP TABLE patients; --")},
        {"filter", std::string("1=1; DROP TABLE test_extract.patients; --")}
    };

    omop::core::ProcessingContext context;
    
    // Should either sanitize the input or throw an exception
    EXPECT_THROW(extractor->initialize(config, context), common::ExtractionException);
    
    // Test with properly sanitized inputs
    std::unordered_map<std::string, std::any> safe_config = {
        {"table", std::string("test_extract.patients")},
        {"filter", std::string("patient_id = 1")}
    };
    
    auto safe_extractor = std::make_unique<omop::extract::DatabaseExtractor>(omop::test::DatabaseConnectionFactory::createTestConnection());
    EXPECT_NO_THROW(safe_extractor->initialize(safe_config, context));
    
    auto batch = safe_extractor->extract_batch(10, context);
    EXPECT_GE(batch.size(), 0);
}

// Tests NULL value handling in database results
TEST_F(DatabaseExtractorIntegrationTest, NullValueHandling) {
    auto extractor = std::make_unique<omop::extract::DatabaseExtractor>(omop::test::DatabaseConnectionFactory::createTestConnection());

    std::unordered_map<std::string, std::any> config = {
        {"query", std::string("SELECT patient_id, first_name, middle_name, last_name FROM test_extract.patients WHERE middle_name IS NULL OR middle_name IS NOT NULL")},
        {"null_value_replacement", std::string("")}, // Replace NULLs with empty string
        {"handle_nulls", true}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    auto batch = extractor->extract_batch(10, context);
    
    if (batch.size() > 0) {
        const auto& record = batch.getRecord(0);
        EXPECT_TRUE(record.hasField("patient_id"));
        EXPECT_TRUE(record.hasField("first_name"));
        EXPECT_TRUE(record.hasField("middle_name"));
        EXPECT_TRUE(record.hasField("last_name"));
        
        // NULL values should be handled appropriately
        auto middle_name = record.getField("middle_name");
        if (middle_name.has_value()) {
            // Should either be empty string or actual value
            std::string value = std::any_cast<std::string>(middle_name);
            EXPECT_TRUE(value.empty() || !value.empty());
        }
    }
    
    extractor->finalize(context);
}

// Tests database type mapping to OMOP CDM data types
TEST_F(DatabaseExtractorIntegrationTest, DatabaseTypeMapping) {
    auto extractor = std::make_unique<omop::extract::DatabaseExtractor>(omop::test::DatabaseConnectionFactory::createTestConnection());

    std::unordered_map<std::string, std::any> config = {
        {"query", std::string("SELECT patient_id, birth_date, weight, is_active FROM test_extract.patients LIMIT 1")},
        {"type_mapping", std::unordered_map<std::string, std::string>{
            {"patient_id", "INTEGER"},
            {"birth_date", "DATE"},
            {"weight", "DECIMAL"},
            {"is_active", "BOOLEAN"}
        }},
        {"enable_type_mapping", true}
    };

    omop::core::ProcessingContext context;
    extractor->initialize(config, context);

    auto batch = extractor->extract_batch(1, context);
    
    if (batch.size() > 0) {
        const auto& record = batch.getRecord(0);
        // Verify type mappings are applied correctly
        EXPECT_TRUE(record.hasField("patient_id"));
        EXPECT_TRUE(record.hasField("birth_date"));
        EXPECT_TRUE(record.hasField("weight"));
        EXPECT_TRUE(record.hasField("is_active"));
        
        // Verify data types
        auto patient_id = record.getField("patient_id");
        auto birth_date = record.getField("birth_date");
        auto weight = record.getField("weight");
        auto is_active = record.getField("is_active");
        
        if (patient_id.has_value()) {
            EXPECT_TRUE(std::any_cast<int>(patient_id) >= 0);
        }
        if (birth_date.has_value()) {
            EXPECT_TRUE(std::any_cast<std::string>(birth_date).length() > 0);
        }
        if (weight.has_value()) {
            EXPECT_TRUE(std::any_cast<double>(weight) >= 0.0);
        }
        if (is_active.has_value()) {
            EXPECT_TRUE(std::any_cast<bool>(is_active) || !std::any_cast<bool>(is_active));
        }
    }
    
    extractor->finalize(context);
}

} // namespace omop::test