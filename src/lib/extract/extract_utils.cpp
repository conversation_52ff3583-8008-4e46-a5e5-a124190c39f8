/**
 * @file extract_utils.cpp
 * @brief Implementation of utility functions for the extract module
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "extract_utils.h"
#include <filesystem>
#include <regex>
#include <thread>
#include <future>
#include <queue>

namespace omop::extract {

// Utility function implementations

std::unique_ptr<core::IExtractor> create_extractor_auto(
    const std::string& source_path,
    const std::unordered_map<std::string, std::any>& config) {

    auto logger = common::Logger::get("omop-extractor-auto");
    logger->info("Auto-detecting extractor type for: {}", source_path);

    std::string type = detect_source_type(source_path);

    if (type.empty()) {
        throw common::ConfigurationException(
            "Could not determine extractor type for: " + source_path);
    }

    logger->info("Detected extractor type: {}", type);

    // Create configuration with source path
    auto full_config = config;
    if (type == "csv" || type == "compressed_csv" || type == "json" || type == "jsonl") {
        full_config["filepath"] = source_path;
    } else if (type == "csv_directory") {
        full_config["directory"] = source_path;
    }

    return create_extractor(type, full_config);
}

std::string detect_source_type(const std::string& source_path) {
    // Check if it's a file or directory
    if (std::filesystem::exists(source_path)) {
        if (std::filesystem::is_directory(source_path)) {
            // Check if directory contains CSV files
            bool has_csv = false, has_json = false;
            for (const auto& entry : std::filesystem::directory_iterator(source_path)) {
                if (entry.is_regular_file()) {
                    std::string filename = entry.path().filename().string();
                    std::transform(filename.begin(), filename.end(), filename.begin(), ::tolower);
                    if (filename.find(".csv") != std::string::npos) {
                        has_csv = true;
                    } else if (filename.find(".json") != std::string::npos) {
                        has_json = true;
                    }
                }
            }
            if (has_csv) return "csv_directory";
            if (has_json) return "json_directory";
            return "";
        }
    }

    // Check file extension (works for both existing and non-existing files)
    std::filesystem::path path(source_path);
    std::string extension = path.extension().string();
    std::transform(extension.begin(), extension.end(), extension.begin(), ::tolower);

    // Check for compressed CSV
    std::string filename = path.filename().string();
    std::transform(filename.begin(), filename.end(), filename.begin(), ::tolower);

    if (filename.find(".csv.gz") != std::string::npos ||
        filename.find(".csv.zip") != std::string::npos ||
        filename.find(".csv.bz2") != std::string::npos ||
        filename.find(".csv.xz") != std::string::npos) {
        return "compressed_csv";
    }

    // Check regular extensions
    if (extension == ".csv") {
        return "csv";
    } else if (extension == ".json") {
        // If file exists, peek at content to distinguish JSON from JSONL
        if (std::filesystem::exists(path) && std::filesystem::file_size(path) > 0) {
            std::ifstream file(path);
            std::string line;
            if (std::getline(file, line)) {
                // Simple heuristic: if first line contains array bracket, it's JSON
                // Otherwise, assume JSONL
                if (line.find('[') != std::string::npos) {
                    return "json";
                } else {
                    return "jsonl";
                }
            }
        }
        return "json"; // Default to JSON if we can't determine
    } else if (extension == ".jsonl" || extension == ".ndjson") {
        return "jsonl";
    } else if (extension == ".gz" || extension == ".zip" ||
               extension == ".bz2" || extension == ".xz") {
        // Enhanced heuristic for compressed files
        std::string stem = path.stem().string();
        std::transform(stem.begin(), stem.end(), stem.begin(), ::tolower);
        if (stem.find(".csv") != std::string::npos) {
            return "compressed_csv";
        } else if (stem.find(".json") != std::string::npos) {
            return "compressed_json";
        }
        // Default to compressed CSV for unknown compressed files
        return "compressed_csv";
    }

    // Check if it's a database connection string
    std::regex db_regex("^(postgresql|postgres|mysql|mariadb|sqlite|odbc)://", std::regex::icase);
    if (std::regex_search(source_path, db_regex)) {
        // Extract database type from URL
        size_t pos = source_path.find("://");
        if (pos != std::string::npos) {
            std::string type = source_path.substr(0, pos);
            std::transform(type.begin(), type.end(), type.begin(), ::tolower);
            return type;
        }
    }

    // Check for SQL files (could be database scripts)
    if (extension == ".sql") {
        return "sql_script";
    }

    return "";
}

std::pair<bool, std::string> validate_extractor_config(
    const std::string& type,
    const std::unordered_map<std::string, std::any>& config) {

    // Get extractor type info
    auto type_info = get_extractor_info();

    // Find the type
    auto it = std::find_if(type_info.begin(), type_info.end(),
        [&type](const ExtractorTypeInfo& info) { return info.type == type; });

    if (it == type_info.end()) {
        return {false, "Unknown extractor type: '" + type + "'"};
    }

    // Check required parameters
    for (const auto& param : it->required_params) {
        if (config.find(param) == config.end()) {
            return {false, "Missing required parameter: '" + param + "'"};
        }
    }

    // Type-specific validation
    if (type == "csv" || type == "compressed_csv" || type == "json" || type == "jsonl") {
        if (config.find("filepath") != config.end()) {
            std::string filepath;
            try {
                filepath = std::any_cast<std::string>(config.at("filepath"));
            } catch (const std::bad_any_cast&) {
                return {false, "Invalid filepath parameter type"};
            }
            if (!std::filesystem::exists(filepath)) {
                return {false, "File not found: '" + filepath + "'"};
            }
        }
    } else if (type == "csv_directory") {
        if (config.find("directory") != config.end()) {
            std::string directory;
            try {
                directory = std::any_cast<std::string>(config.at("directory"));
            } catch (const std::bad_any_cast&) {
                return {false, "Invalid directory parameter type"};
            }
            if (!std::filesystem::exists(directory) || !std::filesystem::is_directory(directory)) {
                return {false, "Directory not found: '" + directory + "'"};
            }
        }
    }

    return {true, ""};
}

// BatchExtractor implementation

BatchExtractor::BatchExtractor(std::unique_ptr<core::IExtractor> extractor)
    : extractor_(std::move(extractor)), config_() {

    if (!extractor_) {
        throw std::runtime_error("BatchExtractor requires a valid extractor");
    }
}

BatchExtractor::BatchExtractor(std::shared_ptr<core::IExtractor> extractor)
    : extractor_(std::move(extractor)), config_() {

    if (!extractor_) {
        throw std::runtime_error("BatchExtractor requires a valid extractor");
    }
}

BatchExtractor::BatchExtractor(std::unique_ptr<core::IExtractor> extractor,
                             const Config& config)
    : extractor_(std::move(extractor)), config_(config) {

    if (!extractor_) {
        throw std::runtime_error("BatchExtractor requires a valid extractor");
    }
}

BatchExtractor::BatchExtractor(std::shared_ptr<core::IExtractor> extractor,
                             const Config& config)
    : extractor_(std::move(extractor)), config_(config) {

    if (!extractor_) {
        throw std::runtime_error("BatchExtractor requires a valid extractor");
    }
}

std::vector<core::Record> BatchExtractor::extract_all() {
    std::lock_guard<std::mutex> lock(extraction_mutex_);
    
    std::vector<core::Record> all_records;
    size_t total_extracted = 0;

    auto logger = common::Logger::get("omop-batch-extractor");
    logger->info("Starting batch extraction with batch size: {}", config_.batch_size);

    try {
        while (extractor_->has_more_data() &&
               (config_.max_records == 0 || total_extracted < config_.max_records)) {

            size_t batch_size = config_.batch_size;
            if (config_.max_records > 0) {
                size_t remaining = config_.max_records - total_extracted;
                batch_size = std::min(batch_size, remaining);
            }

            auto batch = extractor_->extract_batch(batch_size, context_);

            if (batch.empty()) {
                break;
            }

            // Only add up to the remaining number of records needed
            size_t to_add = batch.size();
            if (config_.max_records > 0 && total_extracted + to_add > config_.max_records) {
                to_add = config_.max_records - total_extracted;
            }
            const auto& batch_records = batch.getRecords();
            all_records.insert(all_records.end(), batch_records.begin(), batch_records.begin() + to_add);
            total_extracted += to_add;

            if (config_.progress_callback) {
                config_.progress_callback(total_extracted, config_.max_records);
            }
            // Stop if we've reached the max_records limit
            if (config_.max_records > 0 && total_extracted >= config_.max_records) {
                break;
            }
        }
    } catch (const std::exception& e) {
        if (config_.error_callback) {
            config_.error_callback(e.what());
        }
        
        // Always re-throw extractor-level exceptions as they indicate infrastructure failures
        throw;
    }

    // Final progress update with actual totals
    if (config_.progress_callback) {
        config_.progress_callback(total_extracted, total_extracted);
    }

    logger->info("Batch extraction completed: {} records extracted", total_extracted);
    return all_records;
}

size_t BatchExtractor::extract_with_callback(
    std::function<void(const core::RecordBatch&)> processor) {

    size_t total_extracted = 0;
    auto logger = common::Logger::get("omop-batch-extractor");

    try {
        while (extractor_->has_more_data() &&
               (config_.max_records == 0 || total_extracted < config_.max_records)) {

            size_t batch_size = config_.batch_size;
            if (config_.max_records > 0) {
                size_t remaining = config_.max_records - total_extracted;
                batch_size = std::min(batch_size, remaining);
            }

            auto batch = extractor_->extract_batch(batch_size, context_);

            if (batch.empty()) {
                break;
            }

            processor(batch);
            total_extracted += batch.size();

            if (config_.progress_callback) {
                config_.progress_callback(total_extracted, config_.max_records);
            }
        }
    } catch (const std::exception& e) {
        if (config_.error_callback) {
            config_.error_callback(e.what());
        }
        
        // Always re-throw extractor-level exceptions as they indicate infrastructure failures
        throw;
    }

    return total_extracted;
}

std::unordered_map<std::string, std::any> BatchExtractor::get_statistics() const {
    auto stats = extractor_->get_statistics();
    stats["batch_size"] = config_.batch_size;
    stats["max_records"] = config_.max_records;
    stats["continue_on_error"] = config_.continue_on_error;
    return stats;
}

void BatchExtractor::set_config(const Config& config) {
    config_ = config;
}

// ParallelExtractor implementation

ParallelExtractor::ParallelExtractor()
    : ParallelExtractor(Config{}) {
}

ParallelExtractor::ParallelExtractor(const Config& config)
    : config_(config) {
    
    auto logger = common::Logger::get("omop-parallel-extractor");
    logger->info("Initializing parallel extractor with {} workers", config.num_workers);
    
    // Start worker threads
    for (size_t i = 0; i < config.num_workers; ++i) {
        workers_.emplace_back(&ParallelExtractor::worker_thread, this);
    }
}

ParallelExtractor::~ParallelExtractor() {
    {
        std::unique_lock<std::mutex> lock(queue_mutex_);
        stop_ = true;
        queue_cv_.notify_all();
    }
    
    // Wait for all workers to finish
    for (auto& worker : workers_) {
        if (worker.joinable()) {
            worker.join();
        }
    }
}

void ParallelExtractor::add_task(std::function<void()> task) {
    {
        std::unique_lock<std::mutex> lock(queue_mutex_);
        task_queue_.push(std::move(task));
    }
    queue_cv_.notify_one();
}

void ParallelExtractor::wait_for_completion() {
    std::unique_lock<std::mutex> lock(queue_mutex_);
    completion_cv_.wait(lock, [this]() { return active_tasks_ == 0 && task_queue_.empty(); });
}

void ParallelExtractor::add_extractor(std::unique_ptr<core::IExtractor> extractor,
                                    const std::string& name) {
    std::string extractor_name = name.empty() ?
        "extractor_" + std::to_string(extractors_.size()) : name;

    extractors_.emplace_back(extractor_name, std::shared_ptr<core::IExtractor>(std::move(extractor)));
}

std::vector<core::Record> ParallelExtractor::extract_all() {
    std::vector<core::Record> all_records;
    std::mutex records_mutex;

    auto logger = common::Logger::get("omop-parallel-extractor");
    logger->info("Starting parallel extraction with {} extractors", extractors_.size());

    // Create tasks for each extractor
    for (const auto& [name, extractor] : extractors_) {
        add_task([name, extractor, &all_records, &records_mutex]() {
            try {
                BatchExtractor batch_extractor(extractor);
                auto records = batch_extractor.extract_all();

                std::unique_lock<std::mutex> lock(records_mutex);
                all_records.insert(all_records.end(), records.begin(), records.end());

            } catch (const std::exception& e) {
                auto logger = common::Logger::get("omop-parallel-extractor");
                logger->error("Extractor '{}' failed: {}", name, e.what());
            }
        });
    }

    wait_for_completion();

    logger->info("Parallel extraction completed: {} total records", all_records.size());
    return all_records;
}

void ParallelExtractor::extract_streaming(
    std::function<void(const core::RecordBatch&, const std::string&)> processor) {

    auto logger = common::Logger::get("omop-parallel-extractor");
    logger->info("Starting parallel streaming extraction");

    // Create tasks for each extractor
    for (const auto& [name, extractor] : extractors_) {
        add_task([name, extractor, &processor]() {
            try {
                core::ProcessingContext context;

                while (extractor->has_more_data()) {
                    auto batch = extractor->extract_batch(10000, context);
                    if (!batch.empty()) {
                        processor(batch, name);
                    }
                }

            } catch (const std::exception& e) {
                auto logger = common::Logger::get("omop-parallel-extractor");
                logger->error("Streaming extractor '{}' failed: {}", name, e.what());
            }
        });
    }

    wait_for_completion();
}

std::unordered_map<std::string, std::unordered_map<std::string, std::any>>
ParallelExtractor::get_all_statistics() const {
    std::unordered_map<std::string, std::unordered_map<std::string, std::any>> stats;

    for (const auto& [name, extractor] : extractors_) {
        stats[name] = extractor->get_statistics();
    }

    return stats;
}

// Private helper methods
void ParallelExtractor::worker_thread() {
    auto logger = common::Logger::get("omop-parallel-extractor");
    
    while (true) {
        std::function<void()> task;
        
        {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            queue_cv_.wait(lock, [this]() { return stop_ || !task_queue_.empty(); });
            
            if (stop_ && task_queue_.empty()) {
                break;
            }
            
            if (!task_queue_.empty()) {
                task = std::move(task_queue_.front());
                task_queue_.pop();
                active_tasks_++;
            }
        }
        
        if (task) {
            try {
                task();
            } catch (const std::exception& e) {
                logger->error("Error in worker thread: {}", e.what());
            }
            
            {
                std::unique_lock<std::mutex> lock(queue_mutex_);
                active_tasks_--;
                if (active_tasks_ == 0 && task_queue_.empty()) {
                    completion_cv_.notify_all();
                }
            }
        }
    }
}

// Utility namespace implementations

namespace utils {

std::vector<core::Record> extract_csv(const std::string& filepath,
                                    const CsvOptions& options) {
    auto logger = common::Logger::get("omop-csv-utils");
    logger->info("Extracting CSV data from: {}", filepath);

    // Create CSV extractor with provided options
    auto extractor = std::make_unique<CsvExtractor>();
    
    // Configure extractor
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    config["delimiter"] = std::string(1, options.delimiter);
    config["quote_char"] = std::string(1, options.quote_char);
    config["escape_char"] = std::string(1, options.escape_char);
    config["has_header"] = options.has_header;
    config["encoding"] = options.encoding;
    config["skip_empty_lines"] = options.skip_empty_lines;
    config["trim_fields"] = options.trim_fields;
    config["skip_lines"] = options.skip_lines;
    if (options.max_lines.has_value()) {
        config["max_lines"] = options.max_lines.value();
    }
    config["null_string"] = options.null_string;
    config["date_format"] = options.date_format;
    config["datetime_format"] = options.datetime_format;
    
    if (!options.column_names.empty()) {
        config["column_names"] = options.column_names;
    }
    if (!options.column_types.empty()) {
        config["column_types"] = options.column_types;
    }

    // Initialize extractor
    core::ProcessingContext context;
    extractor->initialize(config, context);

    // Extract all records using batch processing
    std::unique_ptr<core::IExtractor> extractor_ptr = std::move(extractor);
    BatchExtractor batch_extractor(std::move(extractor_ptr));
    auto records = batch_extractor.extract_all();

    logger->info("Successfully extracted {} records from CSV file", records.size());
    return records;
}

std::vector<core::Record> extract_json(const std::string& filepath,
                                     const JsonOptions& options) {
    auto logger = common::Logger::get("omop-json-utils");
    logger->info("Extracting JSON data from: {}", filepath);

    // Create JSON extractor
    auto extractor = create_extractor("json", {{"filepath", filepath}});
    
    if (!extractor) {
        throw common::ConfigurationException(
            "Failed to create JSON extractor for file: " + filepath);
    }

    // Configure extractor with JSON-specific options
    std::unordered_map<std::string, std::any> config;
    config["filepath"] = filepath;
    
    // JSON extractor specific configuration
    if (!options.root_path.empty()) {
        config["root_path"] = options.root_path;
    }
    config["flatten_nested"] = options.flatten_nested;
    config["array_delimiter"] = options.array_delimiter;
    config["parse_dates"] = options.parse_dates;
    config["date_formats"] = options.date_formats;
    config["ignore_null"] = options.ignore_null;
    config["max_depth"] = options.max_depth;

    // Initialize extractor
    core::ProcessingContext context;
    extractor->initialize(config, context);

    // Extract all records using batch processing
    std::unique_ptr<core::IExtractor> extractor_ptr = std::move(extractor);
    BatchExtractor batch_extractor(std::move(extractor_ptr));
    auto records = batch_extractor.extract_all();

    logger->info("Successfully extracted {} records from JSON file", records.size());
    return records;
}

std::vector<core::Record> extract_table(std::unique_ptr<IDatabaseConnection> connection,
                                      const std::string& table_name,
                                      const std::string& filter) {
    auto logger = common::Logger::get("omop-db-utils");
    logger->info("Extracting table data from: {}", table_name);

    if (!connection || !connection->is_connected()) {
        throw common::DatabaseException(
            "Database connection is not available or not connected", 
            connection ? connection->get_database_type() : "unknown", 0);
    }

    // Create database extractor
    auto extractor = std::make_unique<DatabaseExtractor>(std::move(connection));
    
    // Configure extractor
    std::unordered_map<std::string, std::any> config;
    config["table"] = table_name;
    
    // Parse table name for schema if provided (schema.table format)
    size_t dot_pos = table_name.find('.');
    if (dot_pos != std::string::npos) {
        config["schema"] = table_name.substr(0, dot_pos);
        config["table"] = table_name.substr(dot_pos + 1);
    }
    
    // Add filter condition if provided
    if (!filter.empty()) {
        config["filter"] = filter;
    }

    // Initialize extractor
    core::ProcessingContext context;
    extractor->initialize(config, context);

    // Extract all records using batch processing
    std::unique_ptr<core::IExtractor> extractor_ptr = std::move(extractor);
    BatchExtractor batch_extractor(std::move(extractor_ptr));
    auto records = batch_extractor.extract_all();

    logger->info("Successfully extracted {} records from table {}", records.size(), table_name);
    return records;
}

std::unique_ptr<IDatabaseConnection> create_connection_from_url(const std::string& url) {
    // Parse database URL format: type://username:password@host:port/database
    std::regex url_regex(R"((\w+)://(?:([^:]+):([^@]+)@)?([^:/]+)(?::(\d+))?/(.+))");
    std::smatch matches;

    if (!std::regex_match(url, matches, url_regex)) {
        throw common::ConfigurationException(
            "Invalid database URL format: '" + url + "'");
    }

    std::string type = matches[1].str();
    std::string username = matches[2].str();
    std::string password = matches[3].str();
    std::string host = matches[4].str();
    std::string port_str = matches[5].str();
    std::string database = matches[6].str();

    int port = 0;
    if (!port_str.empty()) {
        port = std::stoi(port_str);
    } else {
        // Default ports
        if (type == "postgresql" || type == "postgres") {
            port = 5432;
        } else if (type == "mysql" || type == "mariadb") {
            port = 3306;
        }
    }

    IDatabaseConnection::ConnectionParams params;
    params.host = host;
    params.port = port;
    params.database = database;
    params.username = username;
    params.password = password;

    auto connection = DatabaseConnectionFactory::instance().create(type, params);

    return connection;
}

} // namespace utils

} // namespace omop::extract