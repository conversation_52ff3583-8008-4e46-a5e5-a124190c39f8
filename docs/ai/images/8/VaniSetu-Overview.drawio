<mxfile host="app.diagrams.net">
  <diagram name="VaniSetu-Overview" id="overview-1">
    <mxGraphModel dx="1600" dy="900" grid="1" gridSize="10" guides="1">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <!-- Title -->
        <mxCell id="2" value="VāṇīSetu Framework Overview" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=18;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="200" y="20" width="600" height="40" as="geometry" />
        </mxCell>
        <!-- Cloud LLM Layer -->
        <mxCell id="3" value="Cloud LLM Services&#xa;(Knowledge Retrieval)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="350" y="80" width="300" height="60" as="geometry" />
        </mxCell>
        <!-- Sanskrit Intermediate Layer -->
        <mxCell id="4" value="VāṇīSetu Sanskrit Protocol&#xa;वाणीसेतु" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="200" y="180" width="600" height="50" as="geometry" />
        </mxCell>
        <!-- DGX Spark -->
        <mxCell id="5" value="NVIDIA DGX Spark&#xa;GB10 Grace Blackwell&#xa;(1 PFLOPS, 72 ARM Cores)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=13;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="200" y="270" width="250" height="80" as="geometry" />
        </mxCell>
        <!-- External FPGA -->
        <mxCell id="6" value="External FPGA&#xa;SNN Emulation&#xa;(Neuromorphic Processing)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=13;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="550" y="270" width="250" height="80" as="geometry" />
        </mxCell>
        <!-- Connections -->
        <mxCell id="7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;" edge="1" parent="1" source="3" target="4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;" edge="1" parent="1" source="4" target="5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#666666;" edge="1" parent="1" source="4" target="6">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <!-- Sanskrit Phonemes -->
        <mxCell id="10" value="क (ka) - Kernel ops&#xa;च (ca) - Cache ops&#xa;ट (ṭa) - Tensor ops&#xa;त (ta) - Time ops&#xa;प (pa) - Parallel ops" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=11;align=left" vertex="1" parent="1">
          <mxGeometry x="50" y="270" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="11" value="Processing Modes" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e6f3ff;strokeColor=#4472c4;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="200" y="380" width="600" height="30" as="geometry" />
        </mxCell>
        <mxCell id="12" value="Cognitive&#xa;(SLM)&#xa;50-100ms" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d4edda;strokeColor=#28a745;fontSize=12" vertex="1" parent="1">
          <mxGeometry x="250" y="430" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="13" value="Reflexive&#xa;(SNN)&#xa;1-10ms" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#cce5ff;strokeColor=#004085;fontSize=12" vertex="1" parent="1">
          <mxGeometry x="450" y="430" width="150" height="60" as="geometry" />
        </mxCell>
        <mxCell id="14" value="Knowledge&#xa;(Cloud LLM)&#xa;100-500ms" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8d7da;strokeColor=#721c24;fontSize=12" vertex="1" parent="1">
          <mxGeometry x="650" y="430" width="150" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
