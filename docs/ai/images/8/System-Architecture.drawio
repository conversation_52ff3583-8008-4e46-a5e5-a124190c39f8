<mxfile host="app.diagrams.net">
  <diagram name="System-Architecture" id="system-arch-3">
    <mxGraphModel dx="1600" dy="900" grid="1" gridSize="10" guides="1">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <!-- Title -->
        <mxCell id="2" value="VāṇīSetu System Architecture" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=16;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="100" y="20" width="800" height="40" as="geometry" />
        </mxCell>
        <!-- DGX Spark Box -->
        <mxCell id="3" value="NVIDIA DGX Spark (Compact Form Factor)" style="swimlane;fontSize=14;fontStyle=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="100" y="80" width="500" height="400" as="geometry" />
        </mxCell>
        <!-- Grace CPU -->
        <mxCell id="4" value="Grace CPU (72 ARM Cores)&#xa;Smṛti Pool स्मृति" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3cd;strokeColor=#856404;fontSize=12" vertex="1" parent="3">
          <mxGeometry x="20" y="40" width="220" height="80" as="geometry" />
        </mxCell>
        <!-- Blackwell GPU -->
        <mxCell id="5" value="Blackwell GPU (1 PFLOPS)&#xa;Śakti Pool शक्ति" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#cce5ff;strokeColor=#004085;fontSize=12" vertex="1" parent="3">
          <mxGeometry x="260" y="40" width="220" height="80" as="geometry" />
        </mxCell>
        <!-- Unified Memory -->
        <mxCell id="6" value="Unified Memory (128GB LPDDR5X)&#xa;Setu Zone सेतु" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;fontStyle=1" vertex="1" parent="3">
          <mxGeometry x="20" y="140" width="460" height="40" as="geometry" />
        </mxCell>
        <!-- Sanskrit Runtime -->
        <mxCell id="7" value="Vāṇī Runtime (Rust)&#xa;वाणी" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=12" vertex="1" parent="3">
          <mxGeometry x="20" y="200" width="150" height="60" as="geometry" />
        </mxCell>
        <!-- CUDA Kernels -->
        <mxCell id="8" value="Setu Kernels (CUDA)&#xa;सेतु" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=12" vertex="1" parent="3">
          <mxGeometry x="190" y="200" width="150" height="60" as="geometry" />
        </mxCell>
        <!-- Translator -->
        <mxCell id="9" value="Prakṛti Translator&#xa;प्रकृति" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=12" vertex="1" parent="3">
          <mxGeometry x="360" y="200" width="120" height="60" as="geometry" />
        </mxCell>
        <!-- PCIe Interface -->
        <mxCell id="10" value="PCIe Gen5 Interface (128 GB/s)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8d7da;strokeColor=#721c24;fontSize=12" vertex="1" parent="3">
          <mxGeometry x="20" y="280" width="460" height="30" as="geometry" />
        </mxCell>
        <!-- Processing Domains -->
        <mxCell id="11" value="Vicāra Domain&#xa;विचार&#xa;(Cognitive: 50-100ms)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d4edda;strokeColor=#28a745;fontSize=11" vertex="1" parent="3">
          <mxGeometry x="20" y="330" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="12" value="Pratibimba Domain&#xa;प्रतिबिम्ब&#xa;(Reflexive: 1-10ms)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#cce5ff;strokeColor=#004085;fontSize=11" vertex="1" parent="3">
          <mxGeometry x="180" y="330" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="13" value="Jñāna Domain&#xa;ज्ञान&#xa;(Knowledge: 100-500ms)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8d7da;strokeColor=#721c24;fontSize=11" vertex="1" parent="3">
          <mxGeometry x="340" y="330" width="140" height="50" as="geometry" />
        </mxCell>
        <!-- External FPGA -->
        <mxCell id="14" value="External FPGA Board" style="swimlane;fontSize=14;fontStyle=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="650" y="80" width="250" height="200" as="geometry" />
        </mxCell>
        <mxCell id="15" value="SNN Emulation&#xa;100K Neurons" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3cd;strokeColor=#856404;fontSize=12" vertex="1" parent="14">
          <mxGeometry x="20" y="40" width="210" height="60" as="geometry" />
        </mxCell>
        <mxCell id="16" value="Spike Encoder/Decoder" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=12" vertex="1" parent="14">
          <mxGeometry x="20" y="120" width="210" height="40" as="geometry" />
        </mxCell>
        <!-- Cloud LLM -->
        <mxCell id="17" value="Cloud LLM Services" style="swimlane;fontSize=14;fontStyle=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="650" y="320" width="250" height="160" as="geometry" />
        </mxCell>
        <mxCell id="18" value="Knowledge Retrieval&#xa;API Gateway" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=12" vertex="1" parent="17">
          <mxGeometry x="20" y="40" width="210" height="60" as="geometry" />
        </mxCell>
        <mxCell id="19" value="Sanskrit Protocol&#xa;Interface" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3cd;strokeColor=#856404;fontSize=12" vertex="1" parent="17">
          <mxGeometry x="20" y="110" width="210" height="40" as="geometry" />
        </mxCell>
        <!-- Connections -->
        <mxCell id="20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#FF6666;" edge="1" parent="1" source="10" target="15">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="21" value="PCIe Gen5" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="20">
          <mxGeometry x="-0.2" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="22" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#6666FF;" edge="1" parent="1" source="13" target="18">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="23" value="Network" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="22">
          <mxGeometry x="-0.2" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
