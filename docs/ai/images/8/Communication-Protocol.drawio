<mxfile host="app.diagrams.net">
  <diagram name="Communication-Protocol" id="protocol-4">
    <mxGraphModel dx="1600" dy="900" grid="1" gridSize="10" guides="1">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <!-- Title -->
        <mxCell id="2" value="Three-Layer Communication Protocol" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=16;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="150" y="20" width="700" height="40" as="geometry" />
        </mxCell>
        <!-- Layers -->
        <mxCell id="3" value="Cloud LLM Layer" style="swimlane;fontSize=14;fontStyle=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="50" y="80" width="250" height="150" as="geometry" />
        </mxCell>
        <mxCell id="4" value="Jñāna Protocol&#xa;ज्ञान&#xa;(Knowledge Queries)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=12" vertex="1" parent="3">
          <mxGeometry x="25" y="40" width="200" height="60" as="geometry" />
        </mxCell>
        <mxCell id="5" value="Async Message Queue" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff3cd;strokeColor=#856404;fontSize=11" vertex="1" parent="3">
          <mxGeometry x="25" y="110" width="200" height="30" as="geometry" />
        </mxCell>
        <!-- SLM Layer -->
        <mxCell id="6" value="SLM Layer (DGX)" style="swimlane;fontSize=14;fontStyle=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="350" y="80" width="250" height="150" as="geometry" />
        </mxCell>
        <mxCell id="7" value="Vicāra Protocol&#xa;विचार&#xa;(Cognitive Processing)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=12" vertex="1" parent="6">
          <mxGeometry x="25" y="40" width="200" height="60" as="geometry" />
        </mxCell>
        <mxCell id="8" value="Sync Request-Response" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d4edda;strokeColor=#28a745;fontSize=11" vertex="1" parent="6">
          <mxGeometry x="25" y="110" width="200" height="30" as="geometry" />
        </mxCell>
        <!-- SNN Layer -->
        <mxCell id="9" value="SNN Layer (FPGA)" style="swimlane;fontSize=14;fontStyle=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="650" y="80" width="250" height="150" as="geometry" />
        </mxCell>
        <mxCell id="10" value="Pratibimba Protocol&#xa;प्रतिबिम्ब&#xa;(Reflexive Processing)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=12" vertex="1" parent="9">
          <mxGeometry x="25" y="40" width="200" height="60" as="geometry" />
        </mxCell>
        <mxCell id="11" value="Stream Processing" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#cce5ff;strokeColor=#004085;fontSize=11" vertex="1" parent="9">
          <mxGeometry x="25" y="110" width="200" height="30" as="geometry" />
        </mxCell>
        <!-- Sanskrit Protocol Hub -->
        <mxCell id="12" value="VāṇīSetu Protocol Hub" style="swimlane;fontSize=14;fontStyle=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="200" y="270" width="500" height="200" as="geometry" />
        </mxCell>
        <mxCell id="13" value="Sanskrit Message Router" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=12" vertex="1" parent="12">
          <mxGeometry x="150" y="40" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="14" value="Anuprāsa&#xa;अनुप्रास&#xa;(Sync)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d4edda;strokeColor=#28a745;fontSize=11" vertex="1" parent="12">
          <mxGeometry x="20" y="100" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="15" value="Pravāha&#xa;प्रवाह&#xa;(Stream)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#cce5ff;strokeColor=#004085;fontSize=11" vertex="1" parent="12">
          <mxGeometry x="180" y="100" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="16" value="Saṃdeśa&#xa;संदेश&#xa;(Async)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8d7da;strokeColor=#721c24;fontSize=11" vertex="1" parent="12">
          <mxGeometry x="340" y="100" width="140" height="50" as="geometry" />
        </mxCell>
        <!-- State Machine -->
        <mxCell id="17" value="Protocol State Machine" style="swimlane;fontSize=14;fontStyle=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="1">
          <mxGeometry x="50" y="500" width="850" height="180" as="geometry" />
        </mxCell>
        <mxCell id="18" value="Idle&#xa;निष्क्रिय" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="17">
          <mxGeometry x="50" y="60" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="19" value="Processing&#xa;प्रसंस्करण" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d4edda;strokeColor=#28a745;" vertex="1" parent="17">
          <mxGeometry x="250" y="60" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="20" value="Waiting&#xa;प्रतीक्षा" style="ellipse;whiteSpace=wrap;html=1;fillColor=#fff3cd;strokeColor=#856404;" vertex="1" parent="17">
          <mxGeometry x="450" y="60" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="21" value="Complete&#xa;पूर्ण" style="ellipse;whiteSpace=wrap;html=1;fillColor=#cce5ff;strokeColor=#004085;" vertex="1" parent="17">
          <mxGeometry x="650" y="60" width="100" height="60" as="geometry" />
        </mxCell>
        <!-- Transitions -->
        <mxCell id="22" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="17" source="18" target="19">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="23" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="17" source="19" target="20">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="24" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="17" source="20" target="21">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="25" style="edgeStyle=orthogonalEdgeStyle;rounded=0;curved=1;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="17" source="21" target="18">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="700" y="140" />
              <mxPoint x="100" y="140" />
            </Array>
          </mxGeometry>
        </mxCell>
        <!-- Connections between layers and hub -->
        <mxCell id="26" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" edge="1" parent="1" source="4" target="13">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="27" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" edge="1" parent="1" source="7" target="13">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="28" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;" edge="1" parent="1" source="10" target="13">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
