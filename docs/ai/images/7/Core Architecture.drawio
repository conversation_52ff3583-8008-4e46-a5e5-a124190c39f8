<mxfile host="app.diagrams.net">
  <diagram id="core-arch" name="Core Architecture">
    <mxGraphModel dx="930" dy="655" grid="1" gridSize="10">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>
        
<mxCell id="2" value="Bhasha C++ Framework" style="rounded=1;fillColor=#dae8fc;" vertex="1" parent="1">
    <mxGeometry x="100" y="20" width="200" height="50" as="geometry"/>
</mxCell>
<mxCell id="3" value="Small Language Model (SLM)" style="rounded=1;fillColor=#e1d5e7;" vertex="1" parent="1">
    <mxGeometry x="20" y="100" width="180" height="50" as="geometry"/>
</mxCell>
<mxCell id="4" value="Spiking Neural Network (SNN)" style="rounded=1;fillColor=#fff2cc;" vertex="1" parent="1">
    <mxGeometry x="300" y="100" width="180" height="50" as="geometry"/>
</mxCell>
<mxCell id="5" value="Orchestration Layer" style="rounded=1;fillColor=#f8cecc;" vertex="1" parent="1">
    <mxGeometry x="160" y="200" width="180" height="50" as="geometry"/>
</mxCell>
<mxCell id="6" value="Shared Unified Memory (GPU)" style="shape=cloud;fillColor=#d5e8d4;" vertex="1" parent="1">
    <mxGeometry x="180" y="300" width="180" height="80" as="geometry"/>
</mxCell>
<mxCell id="7" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;" edge="1" source="2" target="3" parent="1">
    <mxGeometry relative="1" as="geometry"/>
</mxCell>
<mxCell id="8" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;" edge="1" source="2" target="4" parent="1">
    <mxGeometry relative="1" as="geometry"/>
</mxCell>
<mxCell id="9" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;" edge="1" source="2" target="5" parent="1">
    <mxGeometry relative="1" as="geometry"/>
</mxCell>
<mxCell id="10" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;" edge="1" source="3" target="6" parent="1">
    <mxGeometry relative="1" as="geometry"/>
</mxCell>
<mxCell id="11" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;" edge="1" source="4" target="6" parent="1">
    <mxGeometry relative="1" as="geometry"/>
</mxCell>
<mxCell id="12" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;" edge="1" source="5" target="6" parent="1">
    <mxGeometry relative="1" as="geometry"/>
</mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
