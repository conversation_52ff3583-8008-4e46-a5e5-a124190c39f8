<mxfile host="app.diagrams.net">
  <diagram id="memory-arch" name="Memory-Architecture">
    <mxGraphModel dx="800" dy="600" grid="1" gridSize="10">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>
        <mxCell id="2" value="Grace CPU (Arm Cortex-A78AE)" style="rounded=1;fillColor=#d5e8d4;" vertex="1" parent="1">
          <mxGeometry x="40" y="20" width="200" height="50" as="geometry"/>
        </mxCell>
        <mxCell id="3" value="Blackwell GPU (GB10 Tensor Cores)" style="rounded=1;fillColor=#dae8fc;" vertex="1" parent="1">
          <mxGeometry x="260" y="20" width="200" height="50" as="geometry"/>
        </mxCell>
        <mxCell id="4" value="Unified System Memory\n(128 GB LPDDR5X)" style="shape=rectangle;rounded=0;fillColor=#fff2cc;" vertex="1" parent="1">
          <mxGeometry x="150" y="100" width="220" height="100" as="geometry"/>
        </mxCell>
        <mxCell id="5" value="Data Flow / Zero-copy access" style="edgeStyle=elbowConnector;orthogonalLoop=1;" edge="1" source="2" target="4" parent="1">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <mxCell id="6" value="" style="edgeStyle=elbowConnector;orthogonalLoop=1;" edge="1" source="3" target="4" parent="1">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <mxCell id="7" value="SLM (Tensor Data)" style="rounded=1;fillColor=#e1d5e7;" vertex="1" parent="4">
          <mxGeometry x="20" y="10" width="80" height="30" as="geometry"/>
        </mxCell>
        <mxCell id="8" value="SNN (Spike Data)" style="rounded=1;fillColor=#f8cecc;" vertex="1" parent="4">
          <mxGeometry x="120" y="10" width="80" height="30" as="geometry"/>
        </mxCell>
        <mxCell id="9" value="Intermediate Bhasha Buffers" style="rounded=1;fillColor=#d5e8d4;" vertex="1" parent="4">
          <mxGeometry x="220" y="10" width="100" height="30" as="geometry"/>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
