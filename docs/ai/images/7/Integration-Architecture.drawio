<mxfile host="app.diagrams.net">
  <diagram id="integration-pipeline" name="Integration-Architecture">
    <mxGraphModel dx="1000" dy="800" grid="1" gridSize="10">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>
        <mxCell id="2" value="Bhasha Model Specification (DSL)" style="rounded=1;fillColor=#ffe6cc;" vertex="1" parent="1">
          <mxGeometry x="40" y="20" width="180" height="50" as="geometry"/>
        </mxCell>
        <mxCell id="3" value="Bhasha C++ Compiler/Interpreter" style="rounded=1;fillColor=#fff2cc;" vertex="1" parent="1">
          <mxGeometry x="260" y="20" width="200" height="50" as="geometry"/>
        </mxCell>
        <mxCell id="4" value="SLM Runtime (C++ / Tensor Ops)" style="rounded=1;fillColor=#e1d5e7;" vertex="1" parent="1">
          <mxGeometry x="50" y="120" width="200" height="50" as="geometry"/>
        </mxCell>
        <mxCell id="5" value="SNN Runtime (C++ / Spiking Ops)" style="rounded=1;fillColor=#f8cecc;" vertex="1" parent="1">
          <mxGeometry x="300" y="120" width="200" height="50" as="geometry"/>
        </mxCell>
        <mxCell id="6" value="Agent Orchestration Logic (C++)" style="rounded=1;fillColor=#d5e8d4;" vertex="1" parent="1">
          <mxGeometry x="550" y="120" width="180" height="50" as="geometry"/>
        </mxCell>
        <mxCell id="7" value="" style="edgeStyle=orthogonalEdgeStyle;endArrow=classic;rounded=0;" edge="1" source="2" target="3" parent="1">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <mxCell id="8" value="" style="edgeStyle=orthogonalEdgeStyle;endArrow=classic;rounded=0;" edge="1" source="3" target="4" parent="1">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <mxCell id="9" value="" style="edgeStyle=orthogonalEdgeStyle;endArrow=classic;rounded=0;" edge="1" source="3" target="5" parent="1">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <mxCell id="10" value="" style="edgeStyle=orthogonalEdgeStyle;endArrow=classic;rounded=0;" edge="1" source="3" target="6" parent="1">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
