<mxfile host="app.diagrams.net">
  <diagram name="Development-Environment" id="dev-env-6">
    <mxGraphModel dx="1400" dy="800" grid="1" gridSize="10" guides="1">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="2" value="Desktop Development Environment" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=16;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="150" y="20" width="500" height="40" as="geometry" />
        </mxCell>
        <mxCell id="3" value="DGX Spark Development Stack" style="swimlane;fontSize=14;fontStyle=1;fillColor=#d4edda;strokeColor=#28a745;" vertex="1" parent="1">
          <mxGeometry x="50" y="80" width="350" height="400" as="geometry" />
        </mxCell>
        <mxCell id="4" value="Operating System" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e6f3ff;strokeColor=#4472c4;fontSize=12;fontStyle=1" vertex="1" parent="3">
          <mxGeometry x="20" y="40" width="310" height="30" as="geometry" />
        </mxCell>
        <mxCell id="5" value="Ubuntu DGX OS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="3">
          <mxGeometry x="20" y="80" width="310" height="25" as="geometry" />
        </mxCell>
        <mxCell id="6" value="Development IDEs" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e6f3ff;strokeColor=#4472c4;fontSize=12;fontStyle=1" vertex="1" parent="3">
          <mxGeometry x="20" y="120" width="310" height="30" as="geometry" />
        </mxCell>
        <mxCell id="7" value="VSCode" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=11" vertex="1" parent="3">
          <mxGeometry x="20" y="160" width="70" height="25" as="geometry" />
        </mxCell>
        <mxCell id="8" value="Jupyter" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=11" vertex="1" parent="3">
          <mxGeometry x="100" y="160" width="70" height="25" as="geometry" />
        </mxCell>
        <mxCell id="9" value="PyCharm" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=11" vertex="1" parent="3">
          <mxGeometry x="180" y="160" width="70" height="25" as="geometry" />
        </mxCell>
        <mxCell id="10" value="Docker" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=11" vertex="1" parent="3">
          <mxGeometry x="260" y="160" width="70" height="25" as="geometry" />
        </mxCell>
        <mxCell id="11" value="ML Frameworks" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e6f3ff;strokeColor=#4472c4;fontSize=12;fontStyle=1" vertex="1" parent="3">
          <mxGeometry x="20" y="200" width="310" height="30" as="geometry" />
        </mxCell>
        <mxCell id="12" value="PyTorch" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=11" vertex="1" parent="3">
          <mxGeometry x="20" y="240" width="70" height="25" as="geometry" />
        </mxCell>
        <mxCell id="13" value="TensorFlow" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=11" vertex="1" parent="3">
          <mxGeometry x="100" y="240" width="70" height="25" as="geometry" />
        </mxCell>
        <mxCell id="14" value="TensorRT" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=11" vertex="1" parent="3">
          <mxGeometry x="180" y="240" width="70" height="25" as="geometry" />
        </mxCell>
        <mxCell id="15" value="CUDA" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=11" vertex="1" parent="3">
          <mxGeometry x="260" y="240" width="70" height="25" as="geometry" />
        </mxCell>
        <mxCell id="16" value="FPGA Tools" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e6f3ff;strokeColor=#4472c4;fontSize=12;fontStyle=1" vertex="1" parent="3">
          <mxGeometry x="20" y="280" width="310" height="30" as="geometry" />
        </mxCell>
        <mxCell id="17" value="Quartus Prime" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=11" vertex="1" parent="3">
          <mxGeometry x="20" y="320" width="90" height="25" as="geometry" />
        </mxCell>
        <mxCell id="18" value="ModelSim" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=11" vertex="1" parent="3">
          <mxGeometry x="120" y="320" width="70" height="25" as="geometry" />
        </mxCell>
        <mxCell id="19" value="Platform Designer" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=11" vertex="1" parent="3">
          <mxGeometry x="200" y="320" width="100" height="25" as="geometry" />
        </mxCell>
        <mxCell id="20" value="DE10-Nano Development" style="swimlane;fontSize=14;fontStyle=1;fillColor=#cce5ff;strokeColor=#004085;" vertex="1" parent="1">
          <mxGeometry x="420" y="80" width="280" height="400" as="geometry" />
        </mxCell>
        <mxCell id="21" value="Embedded Linux" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff3cd;strokeColor=#856404;fontSize=12;fontStyle=1" vertex="1" parent="20">
          <mxGeometry x="20" y="40" width="240" height="30" as="geometry" />
        </mxCell>
        <mxCell id="22" value="Yocto Linux" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="20">
          <mxGeometry x="20" y="80" width="240" height="25" as="geometry" />
        </mxCell>
        <mxCell id="23" value="SNN Frameworks" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff3cd;strokeColor=#856404;fontSize=12;fontStyle=1" vertex="1" parent="20">
          <mxGeometry x="20" y="120" width="240" height="30" as="geometry" />
        </mxCell>
        <mxCell id="24" value="ModNEF" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=11" vertex="1" parent="20">
          <mxGeometry x="20" y="160" width="70" height="25" as="geometry" />
        </mxCell>
        <mxCell id="25" value="Brian2" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=11" vertex="1" parent="20">
          <mxGeometry x="100" y="160" width="70" height="25" as="geometry" />
        </mxCell>
        <mxCell id="26" value="Custom HDL" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=11" vertex="1" parent="20">
          <mxGeometry x="180" y="160" width="80" height="25" as="geometry" />
        </mxCell>
        <mxCell id="27" value="Hardware Access" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff3cd;strokeColor=#856404;fontSize=12;fontStyle=1" vertex="1" parent="20">
          <mxGeometry x="20" y="200" width="240" height="30" as="geometry" />
        </mxCell>
        <mxCell id="28" value="GPIO Control" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=11" vertex="1" parent="20">
          <mxGeometry x="20" y="240" width="80" height="25" as="geometry" />
        </mxCell>
        <mxCell id="29" value="ADC Driver" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=11" vertex="1" parent="20">
          <mxGeometry x="110" y="240" width="70" height="25" as="geometry" />
        </mxCell>
        <mxCell id="30" value="USB Comm" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=11" vertex="1" parent="20">
          <mxGeometry x="190" y="240" width="70" height="25" as="geometry" />
        </mxCell>
        <mxCell id="31" value="Debug Tools" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff3cd;strokeColor=#856404;fontSize=12;fontStyle=1" vertex="1" parent="20">
          <mxGeometry x="20" y="280" width="240" height="30" as="geometry" />
        </mxCell>
        <mxCell id="32" value="SignalTap II" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=11" vertex="1" parent="20">
          <mxGeometry x="20" y="320" width="80" height="25" as="geometry" />
        </mxCell>
        <mxCell id="33" value="Logic Analyzer" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=11" vertex="1" parent="20">
          <mxGeometry x="110" y="320" width="90" height="25" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
