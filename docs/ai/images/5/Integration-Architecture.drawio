<mxfile host="app.diagrams.net">
  <diagram name="Integration-Architecture" id="integration-arch-3">
    <mxGraphModel dx="1600" dy="900" grid="1" gridSize="10" guides="1">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="2" value="Desktop Hybrid System Integration" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=16;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="100" y="20" width="700" height="40" as="geometry" />
        </mxCell>
        <mxCell id="3" value="Sensor Input Layer" style="swimlane;fontSize=14;fontStyle=1;fillColor=#e6f3ff;strokeColor=#4472c4;" vertex="1" parent="1">
          <mxGeometry x="100" y="80" width="700" height="80" as="geometry" />
        </mxCell>
        <mxCell id="4" value="Camera" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="3">
          <mxGeometry x="20" y="30" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="5" value="Microphone" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="3">
          <mxGeometry x="120" y="30" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="6" value="IoT Sensors" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="3">
          <mxGeometry x="220" y="30" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="7" value="Arduino Shield" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="3">
          <mxGeometry x="320" y="30" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="8" value="GPIO Direct" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="3">
          <mxGeometry x="440" y="30" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="9" value="DE10-Nano Reflexive Layer" style="swimlane;fontSize=14;fontStyle=1;fillColor=#cce5ff;strokeColor=#004085;" vertex="1" parent="1">
          <mxGeometry x="100" y="180" width="700" height="100" as="geometry" />
        </mxCell>
        <mxCell id="10" value="Spike&#xa;Encoder" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3cd;strokeColor=#856404;" vertex="1" parent="9">
          <mxGeometry x="20" y="35" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="11" value="SNN Processing&#xa;100K Neurons&#xa;0.5ms latency" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8d7da;strokeColor=#721c24;fontSize=11;fontStyle=1" vertex="1" parent="9">
          <mxGeometry x="120" y="30" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="12" value="Pattern&#xa;Detection" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d1ecf1;strokeColor=#17a2b8;" vertex="1" parent="9">
          <mxGeometry x="260" y="35" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="13" value="Local&#xa;Response" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e7f5e7;strokeColor=#5cb85c;" vertex="1" parent="9">
          <mxGeometry x="360" y="35" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="14" value="ARM Linux&#xa;Controller" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d4edda;strokeColor=#28a745;" vertex="1" parent="9">
          <mxGeometry x="460" y="35" width="90" height="40" as="geometry" />
        </mxCell>
        <mxCell id="15" value="Communication Layer" style="swimlane;fontSize=14;fontStyle=1;fillColor=#fff3cd;strokeColor=#856404;" vertex="1" parent="1">
          <mxGeometry x="100" y="300" width="700" height="80" as="geometry" />
        </mxCell>
        <mxCell id="16" value="USB 3.0&#xa;5 Gbps" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d4edda;strokeColor=#28a745;fontSize=11;fontStyle=1" vertex="1" parent="15">
          <mxGeometry x="100" y="25" width="120" height="40" as="geometry" />
        </mxCell>
        <mxCell id="17" value="Binary Spike&#xa;Protocol" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="15">
          <mxGeometry x="240" y="30" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="18" value="JSON Command&#xa;Messages" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="15">
          <mxGeometry x="360" y="30" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="19" value="Time Sync" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="15">
          <mxGeometry x="480" y="30" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="20" value="DGX Spark Cognitive Layer" style="swimlane;fontSize=14;fontStyle=1;fillColor=#d4edda;strokeColor=#28a745;" vertex="1" parent="1">
          <mxGeometry x="100" y="400" width="700" height="100" as="geometry" />
        </mxCell>
        <mxCell id="21" value="Spike Data&#xa;Receiver" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e6f3ff;strokeColor=#4472c4;" vertex="1" parent="20">
          <mxGeometry x="20" y="35" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="22" value="SLM Inference&#xa;1B-7B params&#xa;50-100ms" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#cce5ff;strokeColor=#004085;fontSize=11;fontStyle=1" vertex="1" parent="20">
          <mxGeometry x="120" y="30" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="23" value="Context&#xa;Analysis" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d1ecf1;strokeColor=#17a2b8;" vertex="1" parent="20">
          <mxGeometry x="260" y="35" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="24" value="Decision&#xa;Engine" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3cd;strokeColor=#856404;" vertex="1" parent="20">
          <mxGeometry x="360" y="35" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="25" value="Response&#xa;Generator" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8d7da;strokeColor=#721c24;" vertex="1" parent="20">
          <mxGeometry x="460" y="35" width="90" height="40" as="geometry" />
        </mxCell>
        <mxCell id="26" value="Application Output Layer" style="swimlane;fontSize=14;fontStyle=1;fillColor=#e6f3ff;strokeColor=#4472c4;" vertex="1" parent="1">
          <mxGeometry x="100" y="520" width="700" height="60" as="geometry" />
        </mxCell>
        <mxCell id="27" value="Display" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="26">
          <mxGeometry x="50" y="20" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="28" value="Actuators" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="26">
          <mxGeometry x="180" y="20" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="29" value="Network API" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="26">
          <mxGeometry x="310" y="20" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="30" value="Storage" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="26">
          <mxGeometry x="440" y="20" width="80" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
