<mxfile host="app.diagrams.net">
  <diagram name="Physical-Setup" id="physical-setup-5">
    <mxGraphModel dx="1400" dy="800" grid="1" gridSize="10" guides="1">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="2" value="Desktop Physical Configuration" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=16;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="150" y="20" width="500" height="40" as="geometry" />
        </mxCell>
        <mxCell id="3" value="Office Desk (Top View)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e6f3ff;strokeColor=#4472c4;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="150" y="80" width="500" height="30" as="geometry" />
        </mxCell>
        <mxCell id="4" value="Monitor 1&#xa;(Primary)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="1">
          <mxGeometry x="250" y="130" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="5" value="Monitor 2&#xa;(Optional)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="1">
          <mxGeometry x="430" y="130" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="6" value="Keyboard" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="1">
          <mxGeometry x="270" y="210" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="7" value="Mouse" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="1">
          <mxGeometry x="390" y="210" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="8" value="DGX Spark&#xa;Personal AI&#xa;Computer" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d4edda;strokeColor=#28a745;fontSize=12;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="170" y="260" width="140" height="80" as="geometry" />
        </mxCell>
        <mxCell id="9" value="DE10-Nano&#xa;in Case" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#cce5ff;strokeColor=#004085;fontSize=12;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="330" y="260" width="100" height="80" as="geometry" />
        </mxCell>
        <mxCell id="10" value="Sensor&#xa;Array" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3cd;strokeColor=#856404;" vertex="1" parent="1">
          <mxGeometry x="450" y="260" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="11" value="USB 3.0" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#FF6666;" edge="1" parent="1" source="8" target="9">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="12" value="GPIO" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#66B2FF;" edge="1" parent="1" source="9" target="10">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="13" value="Power Distribution" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e6f3ff;strokeColor=#4472c4;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="150" y="360" width="500" height="30" as="geometry" />
        </mxCell>
        <mxCell id="14" value="Surge Protector&#xa;1000W Rating" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8d7da;strokeColor=#721c24;" vertex="1" parent="1">
          <mxGeometry x="170" y="410" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="15" value="DGX Power&#xa;800W Max" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d4edda;strokeColor=#28a745;" vertex="1" parent="1">
          <mxGeometry x="310" y="410" width="100" height="50" as="geometry" />
        </mxCell>
        <mxCell id="16" value="DE10 Power&#xa;USB/5V 2A" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#cce5ff;strokeColor=#004085;" vertex="1" parent="1">
          <mxGeometry x="430" y="410" width="100" height="50" as="geometry" />
        </mxCell>
        <mxCell id="17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="14" target="15">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="14" target="16">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
