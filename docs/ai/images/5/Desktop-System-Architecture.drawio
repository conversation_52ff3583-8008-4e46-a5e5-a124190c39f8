<mxfile host="app.diagrams.net">
  <diagram name="Desktop-System-Architecture" id="desktop-arch-1">
    <mxGraphModel dx="1400" dy="800" grid="1" gridSize="10" guides="1">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="2" value="Desktop Hybrid AI System" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=16;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="200" y="20" width="400" height="40" as="geometry" />
        </mxCell>
        <mxCell id="3" value="Office Desk Setup" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e6f3ff;strokeColor=#4472c4;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="200" y="80" width="400" height="30" as="geometry" />
        </mxCell>
        <mxCell id="4" value="NVIDIA DGX Spark&#xa;Personal AI Computer&#xa;1 PFLOPS Performance&#xa;£2,259.47" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d4edda;strokeColor=#28a745;fontSize=12;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="220" y="130" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="5" value="Terasic DE10-Nano&#xa;FPGA Dev Kit&#xa;Cyclone V SoC&#xa;£108.00" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#cce5ff;strokeColor=#004085;fontSize=12;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="420" y="130" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="6" value="Direct Connection&#xa;USB 3.0 / PCIe Adapter&#xa;&lt;10μs Latency" style="shape=doubleArrow;whiteSpace=wrap;html=1;fillColor=#fff3cd;strokeColor=#856404;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="380" y="155" width="40" height="30" as="geometry" />
        </mxCell>
        <mxCell id="7" value="Monitor" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="120" y="150" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="8" value="Keyboard/Mouse" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="120" y="200" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="9" value="Standard Power&#xa;Socket (800W)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="600" y="150" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="10" value="Network&#xa;(Optional)" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="600" y="200" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="11" value="System Capabilities" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e6f3ff;strokeColor=#4472c4;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="200" y="250" width="400" height="30" as="geometry" />
        </mxCell>
        <mxCell id="12" value="Cognitive Processing&#xa;(DGX Spark)&#xa;50-100ms latency&#xa;SLMs, NLP, Planning" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e7f5e7;strokeColor=#5cb85c;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="220" y="300" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="13" value="Reflexive Processing&#xa;(DE10-Nano)&#xa;0.5ms latency&#xa;SNNs, Pattern Detection" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d1ecf1;strokeColor=#17a2b8;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="420" y="300" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="14" value="Total Cost: £2,367.47" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d4edda;strokeColor=#28a745;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="200" y="380" width="400" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
