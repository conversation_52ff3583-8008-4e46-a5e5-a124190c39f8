<mxfile host="app.diagrams.net">
  <diagram name="Performance-Metrics" id="performance-7">
    <mxGraphModel dx="1400" dy="800" grid="1" gridSize="10" guides="1">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="2" value="Desktop System Performance Metrics" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=16;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="150" y="20" width="500" height="40" as="geometry" />
        </mxCell>
        <mxCell id="3" value="Computational Performance" style="swimlane;fontSize=14;fontStyle=1;fillColor=#d4edda;strokeColor=#28a745;" vertex="1" parent="1">
          <mxGeometry x="50" y="80" width="300" height="140" as="geometry" />
        </mxCell>
        <mxCell id="4" value="DGX Spark: 1 PFLOPS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e6f3ff;strokeColor=#4472c4;fontStyle=1" vertex="1" parent="3">
          <mxGeometry x="20" y="30" width="260" height="30" as="geometry" />
        </mxCell>
        <mxCell id="5" value="SLM: 10K tokens/sec" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="3">
          <mxGeometry x="20" y="70" width="120" height="25" as="geometry" />
        </mxCell>
        <mxCell id="6" value="DE10: 100K neurons" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="3">
          <mxGeometry x="160" y="70" width="120" height="25" as="geometry" />
        </mxCell>
        <mxCell id="7" value="Latency: &lt;101ms end-to-end" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="3">
          <mxGeometry x="20" y="105" width="260" height="25" as="geometry" />
        </mxCell>
        <mxCell id="8" value="Cost Efficiency" style="swimlane;fontSize=14;fontStyle=1;fillColor=#cce5ff;strokeColor=#004085;" vertex="1" parent="1">
          <mxGeometry x="370" y="80" width="330" height="140" as="geometry" />
        </mxCell>
        <mxCell id="9" value="Total Cost: £2,367.47" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3cd;strokeColor=#856404;fontStyle=1" vertex="1" parent="8">
          <mxGeometry x="20" y="30" width="290" height="30" as="geometry" />
        </mxCell>
        <mxCell id="10" value="Cloud Equivalent: £500-1000/month" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="8">
          <mxGeometry x="20" y="70" width="230" height="25" as="geometry" />
        </mxCell>
        <mxCell id="11" value="ROI: 3-5 months" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d4edda;strokeColor=#28a745;fontStyle=1" vertex="1" parent="8">
          <mxGeometry x="20" y="105" width="120" height="25" as="geometry" />
        </mxCell>
        <mxCell id="12" value="Energy Efficiency" style="swimlane;fontSize=14;fontStyle=1;fillColor=#fff3cd;strokeColor=#856404;" vertex="1" parent="1">
          <mxGeometry x="50" y="240" width="300" height="140" as="geometry" />
        </mxCell>
        <mxCell id="13" value="Peak Power: 825W" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8d7da;strokeColor=#721c24;" vertex="1" parent="12">
          <mxGeometry x="20" y="30" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="14" value="Average: 400-500W" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d4edda;strokeColor=#28a745;" vertex="1" parent="12">
          <mxGeometry x="160" y="30" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="15" value="95% reduction vs LLMs" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e6f3ff;strokeColor=#4472c4;fontStyle=1" vertex="1" parent="12">
          <mxGeometry x="20" y="70" width="260" height="25" as="geometry" />
        </mxCell>
        <mxCell id="16" value="Office-compatible thermal profile" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="12">
          <mxGeometry x="20" y="105" width="260" height="25" as="geometry" />
        </mxCell>
        <mxCell id="17" value="Scalability" style="swimlane;fontSize=14;fontStyle=1;fillColor=#f8d7da;strokeColor=#721c24;" vertex="1" parent="1">
          <mxGeometry x="370" y="240" width="330" height="140" as="geometry" />
        </mxCell>
        <mxCell id="18" value="Multiple DE10-Nano boards supported" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="17">
          <mxGeometry x="20" y="30" width="290" height="25" as="geometry" />
        </mxCell>
        <mxCell id="19" value="Network expansion via Ethernet" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="17">
          <mxGeometry x="20" y="65" width="290" height="25" as="geometry" />
        </mxCell>
        <mxCell id="20" value="Cloud hybrid options available" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="17">
          <mxGeometry x="20" y="100" width="290" height="25" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
