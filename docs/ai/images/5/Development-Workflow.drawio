<mxfile host="app.diagrams.net">
  <diagram name="Development-Workflow" id="dev-workflow-4">
    <mxGraphModel dx="1400" dy="800" grid="1" gridSize="10" guides="1">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="2" value="Desktop Development Workflow" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=16;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="150" y="20" width="500" height="40" as="geometry" />
        </mxCell>
        <mxCell id="3" value="Design Phase" style="swimlane;fontSize=14;fontStyle=1;fillColor=#e6f3ff;strokeColor=#4472c4;" vertex="1" parent="1">
          <mxGeometry x="50" y="80" width="700" height="80" as="geometry" />
        </mxCell>
        <mxCell id="4" value="SNN Design&#xa;Simulation" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#cce5ff;strokeColor=#004085;" vertex="1" parent="3">
          <mxGeometry x="20" y="30" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="5" value="SLM Model&#xa;Architecture" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d4edda;strokeColor=#28a745;" vertex="1" parent="3">
          <mxGeometry x="140" y="30" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="6" value="Integration&#xa;Protocol" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3cd;strokeColor=#856404;" vertex="1" parent="3">
          <mxGeometry x="260" y="30" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="7" value="Test Case&#xa;Definition" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8d7da;strokeColor=#721c24;" vertex="1" parent="3">
          <mxGeometry x="380" y="30" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="8" value="Implementation Phase" style="swimlane;fontSize=14;fontStyle=1;fillColor=#d4edda;strokeColor=#28a745;" vertex="1" parent="1">
          <mxGeometry x="50" y="180" width="700" height="100" as="geometry" />
        </mxCell>
        <mxCell id="9" value="FPGA Synthesis&#xa;(Quartus on DGX)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#cce5ff;strokeColor=#004085;fontSize=11" vertex="1" parent="8">
          <mxGeometry x="20" y="35" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="10" value="Program DE10&#xa;USB-Blaster" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3cd;strokeColor=#856404;fontSize=11" vertex="1" parent="8">
          <mxGeometry x="160" y="35" width="100" height="50" as="geometry" />
        </mxCell>
        <mxCell id="11" value="SLM Training&#xa;GPU Accelerated" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d4edda;strokeColor=#28a745;fontSize=11" vertex="1" parent="8">
          <mxGeometry x="280" y="35" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="12" value="Deploy&#xa;Integration Code" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d1ecf1;strokeColor=#17a2b8;fontSize=11" vertex="1" parent="8">
          <mxGeometry x="420" y="35" width="100" height="50" as="geometry" />
        </mxCell>
        <mxCell id="13" value="Testing Phase" style="swimlane;fontSize=14;fontStyle=1;fillColor=#f8d7da;strokeColor=#721c24;" vertex="1" parent="1">
          <mxGeometry x="50" y="300" width="700" height="100" as="geometry" />
        </mxCell>
        <mxCell id="14" value="SNN Unit&#xa;Tests" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e6f3ff;strokeColor=#4472c4;" vertex="1" parent="13">
          <mxGeometry x="20" y="40" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="15" value="SLM&#xa;Benchmarks" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e6f3ff;strokeColor=#4472c4;" vertex="1" parent="13">
          <mxGeometry x="120" y="40" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="16" value="USB Comm&#xa;Validation" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e6f3ff;strokeColor=#4472c4;" vertex="1" parent="13">
          <mxGeometry x="220" y="40" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="17" value="End-to-End&#xa;Testing" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e6f3ff;strokeColor=#4472c4;" vertex="1" parent="13">
          <mxGeometry x="340" y="40" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="18" value="Performance&#xa;Analysis" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e6f3ff;strokeColor=#4472c4;" vertex="1" parent="13">
          <mxGeometry x="460" y="40" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="19" value="Deployment" style="swimlane;fontSize=14;fontStyle=1;fillColor=#fff3cd;strokeColor=#856404;" vertex="1" parent="1">
          <mxGeometry x="50" y="420" width="700" height="80" as="geometry" />
        </mxCell>
        <mxCell id="20" value="System&#xa;Assembly" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="19">
          <mxGeometry x="40" y="30" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="21" value="Configuration&#xa;Deployment" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="19">
          <mxGeometry x="180" y="30" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="22" value="Production&#xa;Monitoring" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="19">
          <mxGeometry x="320" y="30" width="100" height="40" as="geometry" />
        </mxCell>
        <mxCell id="23" value="Documentation" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;" vertex="1" parent="19">
          <mxGeometry x="460" y="30" width="100" height="40" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
