<mxfile host="app.diagrams.net">
  <diagram name="DE10-Nano-Architecture" id="de10-nano-arch-2">
    <mxGraphModel dx="1400" dy="800" grid="1" gridSize="10" guides="1">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="2" value="Terasic DE10-Nano Architecture" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=16;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="150" y="20" width="500" height="40" as="geometry" />
        </mxCell>
        <mxCell id="3" value="Intel Cyclone V SoC" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="150" y="80" width="500" height="30" as="geometry" />
        </mxCell>
        <mxCell id="4" value="FPGA Fabric&#xa;110K Logic Elements&#xa;112 DSP Blocks&#xa;4,450 Kbits RAM" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#cce5ff;strokeColor=#004085;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="170" y="130" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="5" value="ARM Cortex-A9&#xa;Dual Core&#xa;925 MHz&#xa;HPS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d4edda;strokeColor=#28a745;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="330" y="130" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="6" value="1GB DDR3&#xa;HPS Memory" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8d7da;strokeColor=#721c24;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="490" y="130" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="7" value="Neuromorphic Implementation" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e6f3ff;strokeColor=#4472c4;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="150" y="220" width="500" height="30" as="geometry" />
        </mxCell>
        <mxCell id="8" value="LIF Neuron&#xa;Array&#xa;(100K neurons)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3cd;strokeColor=#856404;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="170" y="270" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="9" value="Synaptic&#xa;Weight&#xa;Memory" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d1ecf1;strokeColor=#17a2b8;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="290" y="270" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="10" value="AER Router&#xa;Event-Driven" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e7f5e7;strokeColor=#5cb85c;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="410" y="270" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="11" value="Spike&#xa;Encoder/&#xa;Decoder" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8d7da;strokeColor=#721c24;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="530" y="270" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="12" value="I/O Interfaces" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e6f3ff;strokeColor=#4472c4;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="150" y="350" width="500" height="30" as="geometry" />
        </mxCell>
        <mxCell id="13" value="USB OTG" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="170" y="400" width="70" height="40" as="geometry" />
        </mxCell>
        <mxCell id="14" value="GPIO&#xa;2x40 pin" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="260" y="400" width="70" height="40" as="geometry" />
        </mxCell>
        <mxCell id="15" value="Gigabit&#xa;Ethernet" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="350" y="400" width="70" height="40" as="geometry" />
        </mxCell>
        <mxCell id="16" value="Arduino&#xa;Headers" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="440" y="400" width="70" height="40" as="geometry" />
        </mxCell>
        <mxCell id="17" value="HDMI&#xa;Output" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;fontSize=11" vertex="1" parent="1">
          <mxGeometry x="530" y="400" width="70" height="40" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
